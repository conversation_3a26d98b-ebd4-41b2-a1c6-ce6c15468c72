# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
Matiks.app/

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

# vs code settings
*.vscode
*.idea
*.scannerwork
.vercel
android/app/google-services.json

.magicodeconfig


qodana.yaml
qodana.sarif.json

.env
.env.production.local
.env.development.local
!/.scannerwork/
