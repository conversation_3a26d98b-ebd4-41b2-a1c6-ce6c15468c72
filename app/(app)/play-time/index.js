import { View } from 'react-native';

import React, { useEffect } from 'react';
import { useLocalSearchParams } from 'expo-router';
import PlayTime from 'modules/home/<USER>/PlayTime';
import Analytics from '../../../src/core/analytics';
import { ANALYTICS_EVENTS } from '../../../src/core/analytics/const';
import { PAGE_NAMES } from '../../../src/core/constants/pageNames';

import useMediaQuery from '../../../src/core/hooks/useMediaQuery';

const PlayTimeContainer = () => {
  const { gameType } = useLocalSearchParams();
  const { isMobile: isCompactDevice } = useMediaQuery();

  useEffect(() => {
    const pageViewEvent = ANALYTICS_EVENTS[gameType]?.VIEWED;
    if (pageViewEvent) {
      Analytics.track(pageViewEvent);
      webengage?.screen?.(PAGE_NAMES.GAME_CONFIG_FORM);
    }
  }, [gameType]);

  if (!isCompactDevice) {
    return null;
  }

  return (
    <View style={{ flex: 1 }}>
      <PlayTime />
    </View>
  );
};

export default React.memo(PlayTimeContainer);
