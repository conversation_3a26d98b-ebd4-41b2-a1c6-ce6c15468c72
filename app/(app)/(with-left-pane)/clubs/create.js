import CreateClub from '@/src/modules/clubs/pages/CreateClub/CreateClub';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { Redirect } from 'expo-router';
import { showRightPane } from 'molecules/RightPane/RightPane';

const CreateClubPage = () => {
  const { isMobile: isCompactMode } = useMediaQuery();
  if (!isCompactMode) {
    showRightPane({
      content: <CreateClub />,
      style: { justifyContent: 'flex-start' },
    });
    return <Redirect href="/clubs" />;
  }

  return <CreateClub />;
};
export default CreateClubPage;
