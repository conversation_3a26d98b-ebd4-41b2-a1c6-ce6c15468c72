import _isEqual from 'lodash/isEqual';

import { useCallback, useMemo, useState } from 'react';
import { hideToast, showToast, TOAST_TYPE } from 'molecules/Toast';
import { Audio } from 'expo-av';

import { usePathname, useRouter } from 'expo-router';
import fa_notification from '@/assets/audio/fa_notification.mp3';
import _get from 'lodash/get';
import _includes from 'lodash/includes';
import { CHALLENGE_USER_STATUS } from '../constants/challengeUserStatus';
import userReader from '../../core/readers/userReader';
import useGoBack from '../../navigator/hooks/useGoBack';
import { getCurrentActiveGameId } from '../../core/utils/getUserCurrentActivity';

export const PUZZLE_KEY = 'PUZZLE';

const useChallengeUserEvents = () => {
  const { goBack } = useGoBack();

  const router = useRouter();

  const [showChallengeUserOverlay, setShowChallengeUserOverlay] =
    useState(false);

  const currentUrl = usePathname();

  const currActiveGameId = useMemo(
    () => getCurrentActiveGameId({ currentUrl }),
    [currentUrl],
  );

  const playSound = useCallback(async () => {
    try {
      const { sound } = await Audio.Sound.createAsync(fa_notification);
      await sound.playAsync();
    } catch (error) {
      console.log('Error playing sound:', error);
    }
  }, []);

  const handleChallengeUser = useCallback(
    ({ payload }) => {
      const { opponentUser, challengedBy, gameId, gameConfig } =
        payload ?? EMPTY_OBJECT;
      const gameType = _get(gameConfig, 'gameType');
      const waitingTime = 20;
      if (challengedBy === opponentUser?._id) {
        setTimeout(() => playSound(), 500);
        setShowChallengeUserOverlay(true);
      } else if (_includes(gameType, PUZZLE_KEY)) {
        router.push(`/puzzle-game/game/${gameId}`);
      } else {
        router.push(`/game/${gameId}/play`);
      }
      if (waitingTime) {
        setTimeout(() => {
          hideToast();
          setShowChallengeUserOverlay(false);
        }, waitingTime * 1000);
      }
    },
    [playSound, router],
  );

  const handleAcceptChallenge = useCallback(
    ({ payload }) => {
      const { opponentUser, gameId, challengedBy, gameConfig } = payload;
      const gameType = _get(gameConfig, 'gameType');
      hideToast();
      if (challengedBy !== opponentUser?._id) {
        showToast({
          type: TOAST_TYPE.SUCCESS,
          description: `${userReader.username(opponentUser)} Accepted Game Request`,
        });
      }
      if (_includes(gameType, PUZZLE_KEY)) {
        router.push(`/puzzle-game/game/${gameId}`);
      } else {
        router.push(`/game/${gameId}/play`);
      }
    },
    [router],
  );

  const handleRejectChallenge = useCallback(
    ({ payload }) => {
      const { opponentUser, challengedBy, gameId } = payload;
      const isOnSameGame = _isEqual(currActiveGameId, gameId);
      hideToast();
      if (challengedBy !== opponentUser?._id && isOnSameGame) {
        showToast({
          type: TOAST_TYPE.ERROR,
          description: `${userReader.username(opponentUser)} Rejected Game Request`,
        });
        goBack?.();
      }
    },
    [goBack, currActiveGameId],
  );

  const handleRequestTimeout = useCallback(
    ({ payload }) => {
      const { opponentUser, challengedBy, gameId } = payload;
      const isOnSameGame = _isEqual(currActiveGameId, gameId);

      hideToast();
      if (challengedBy !== opponentUser?._id && isOnSameGame) {
        showToast({
          type: TOAST_TYPE.ERROR,
          description: `${userReader.username(opponentUser)} didn't responded to your Request for Game`,
        });
        goBack?.();
      }
    },
    [goBack, currActiveGameId],
  );

  const handleGameCancelled = useCallback(
    ({ payload }) => {
      const { gameId } = payload;
      const isOnSameGame = _isEqual(currActiveGameId, gameId);
      if (isOnSameGame) {
        hideToast();
        showToast({
          type: TOAST_TYPE.SUCCESS,
          description: `Game Cancelled Successfully`,
        });
        goBack?.();
      }
    },
    [goBack, currActiveGameId],
  );

  const handleChallengeUserEvents = useCallback(
    ({ payload }) => {
      const { status } = payload;
      switch (status) {
        case CHALLENGE_USER_STATUS.CHALLENGE_SENT: {
          handleChallengeUser({ payload });
          break;
        }
        case CHALLENGE_USER_STATUS.CHALLENGE_ACCEPTED:
          handleAcceptChallenge({ payload });
          break;
        case CHALLENGE_USER_STATUS.CHALLENGE_REJECTED:
          handleRejectChallenge({ payload });
          break;
        case CHALLENGE_USER_STATUS.CHALLENGE_EXPIRED: {
          handleRequestTimeout({ payload });
          break;
        }
        case CHALLENGE_USER_STATUS.GAME_CANCELLED: {
          handleGameCancelled({ payload });
          break;
        }
        default:
          break;
      }
    },
    [
      handleChallengeUser,
      handleAcceptChallenge,
      handleRejectChallenge,
      handleRequestTimeout,
      handleGameCancelled,
    ],
  );

  return {
    showChallengeUserOverlay,
    handleChallengeUserEvents,
  };
};

export default useChallengeUserEvents;
