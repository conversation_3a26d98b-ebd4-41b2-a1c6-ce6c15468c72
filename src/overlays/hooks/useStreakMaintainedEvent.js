import { useCallback, useRef, useState } from 'react';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import useUpdateCurrentUserOnGameEnd from '../../modules/game/hooks/useUpdateCurrentUserOnGameEnd';

const useHandleStreakMaintainedEvent = () => {
  const [showStreakMaintainedOverlay, setShowStreakMaintainedOverlay] =
    useState(false);
  const { updateUserStreak } = useUpdateCurrentUserOnGameEnd();
  const updateStreakInCacheRef = useRef(updateUserStreak);
  updateStreakInCacheRef.current = updateUserStreak;

  const handleCloseOverlay = useCallback(({ payload }) => {
    setShowStreakMaintainedOverlay(false);
  }, []);

  const handleStreakMaintainedEvent = useCallback(
    ({ payload }) => {
      const { isPlayedToday } = payload;
      if (isPlayedToday) {
        updateStreakInCacheRef?.current();
        Analytics.track(ANALYTICS_EVENTS.STREAKS.STREAK_UPDATED, {
          streakCount: streakCountRef.current,
        });

        setShowStreakMaintainedOverlay(true);
      }
    },
    [updateStreakInCacheRef],
  );

  return {
    showStreakMaintainedOverlay,
    handleCloseOverlay,
    handleStreakMaintainedEvent,
  };
};

export default useHandleStreakMaintainedEvent;
