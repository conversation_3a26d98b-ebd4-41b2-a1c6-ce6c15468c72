import DownloadAppBottomSheet from "../../components/shared/DownloadAppBottomSheet";
import { getStorageState, setStorageItemAsync } from "../hooks/useStorageState";
import { showPopover } from 'molecules/Popover/Popover';
import dark from '../constants/themes/dark';
import { Platform } from "react-native";
import useMediaQuery from "core/hooks/useMediaQuery";
import { useCallback, useEffect } from "react";

const useHandleShowDownloadAppPopover = () => {
    const { isMobile: isCompactMode } = useMediaQuery();
    
    const handleShowDownloadAppPopover = useCallback(async () => {
        if (Platform.OS !== 'web' || !isCompactMode) return;

        const lastShownDate = await getStorageState('lastPopoverShown');
        const currentDate = new Date();
        
        if (lastShownDate) {
            const lastShown = new Date(lastShownDate);
            const daysDifference = Math.floor(
                (currentDate - lastShown) / (1000 * 60 * 60 * 24)
            );

            if (daysDifference < 5) return;
        }

        showPopover({
            content: <DownloadAppBottomSheet />,
            overlayLook: true,
            style: {
                position:"absolute",
                width: '100%',
                bottom: 0,
                borderLeftWidth: 0,
                backgroundColor: dark.colors.background,
                borderTopLeftRadius: 20,
                borderTopRightRadius: 20,
                padding: 13,
            },
        });

        await setStorageItemAsync('lastPopoverShown', currentDate.toISOString().split('T')[0]);
    }, [isCompactMode]);

    useEffect(() => {
        handleShowDownloadAppPopover();
    }, [handleShowDownloadAppPopover]);
};

export default useHandleShowDownloadAppPopover;
