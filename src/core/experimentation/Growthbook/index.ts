import { GrowthBook } from '@growthbook/growthbook';
import { autoAttributesPlugin } from '@growthbook/growthbook/dist/plugins';
import mixpanel from 'core/analytics/Mixpanel';

const growthbook = new GrowthBook({
  apiHost: process.env.EXPO_PUBLIC_GROWTHBOOK_API_HOST,
  clientKey: process.env.EXPO_PUBLIC_GROWTHBOOK_CLIENT_KEY,
  decryptionKey: process.env.EXPO_PUBLIC_GROWTHBOOK_DECRYPTION_KEY,

  enableDevMode: true,
  trackingCallback: (experiment, result) => {
    mixpanel.track('$experiment_started', {
      'Experiment name': experiment.key,
      'Variant name': result.key,
      $source: 'growthbook',
    });
  },
  plugins: [autoAttributesPlugin()],
});

export default growthbook;
