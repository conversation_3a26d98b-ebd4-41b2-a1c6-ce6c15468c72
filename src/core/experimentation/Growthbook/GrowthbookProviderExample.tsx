import { useEffect } from 'react';
import {
  GrowthBookProvider,
  useFeatureIsOn,
  useFeatureValue,
} from '@growthbook/growthbook-react';
import growthbook from 'core/experimentation/Growthbook/index';
import mixpanel from 'core/analytics/Mixpanel';

export const MyApp = () => {
  useEffect(() => {
    // Load features asynchronously when the app renders
    growthbook.init({ streaming: true });
  }, []);

  // Add the mixpanel user id to the GrowthBook attributes when it loads:
  mixpanel.init('[YOUR PROJECT TOKEN]', {
    debug: true,
    loaded(mx) {
      growthbook.updateAttributes({
        distinct_id: mx.get_distinct_id(),
      });
    },
  });

  return (
    <GrowthBookProvider growthbook={growthbook}>
      <MyComponent />
    </GrowthBookProvider>
  );
};

const MyComponent = () => {
  const enabled = useFeatureIsOn('my-feature');
  const value = useFeatureValue('my-feature', 'fallback');

  if (enabled) {
    return (
      <div>
        On!
        <div>{value}</div>
      </div>
    );
  }
  return (
    <div>
      Off!
      <div>{value}</div>
    </div>
  );
};
