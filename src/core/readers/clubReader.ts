import _property from 'lodash/property';

export const clubReader = {
  name: _property('name'),
  description: _property('description'),
  visibility: _property('visibility'),
  forumId: _property('forumId'),
  chatRoomId: _property('chatRoomId'),
  bannerImage: _property('bannerImage'),
  logoImage: _property('logoImage'),
  category: _property('category'),
  eventsCount: _property('clubEventsCount'),
  membersCount: _property('membersCount'),
  isAdmin: _property('isAdmin'),
  isClubMember: _property('isClubMember'),
  createdAt: _property('createdAt'),
  updatedAt: _property('updatedAt'),
  hasRequestedToJoin: _property('hasRequestedToJoin'),
  id: _property('id'),
};

export const clubEventReader = {
  id: _property('_id'),
  name: _property('name'),
  description: _property('description'),
  startTime: _property('startTime'),
  endTime: _property('endTime'),
  isCancelled: _property('isCancelled'),
  createdAt: _property('createdAt'),
  updatedAt: _property('updatedAt'),
  clubId: _property('clubId'),
};

export const clubEventParticipantsReader = {
  id: _property('_id'),
  userId: _property('userId'),
  clubId: _property('clubId'),
  eventId: _property('eventId'),
  createdAt: _property('createdAt'),
  updatedAt: _property('updatedAt'),
};

export const clubThreadReader = {
  id: _property('_id'),
  title: _property('title'),
  description: _property('description'),
  createdAt: _property('createdAt'),
  updatedAt: _property('updatedAt'),
  clubId: _property('clubId'),
};
