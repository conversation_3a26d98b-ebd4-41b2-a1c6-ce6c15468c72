import { useCallback, useEffect, useRef } from 'react';
import { Audio } from 'expo-av';
import _get from 'lodash/get';
import { useUserSettings } from '../contexts/UserSettingsContext';

const useSound = ({
  soundFile,
  config = EMPTY_OBJECT,
  playOnMount = false,
}) => {
  const soundRef = useRef(null);
  const isPlayingRef = useRef(false);
  const { userSettings } = useUserSettings();

  const soundEnabled = _get(userSettings, 'playSound', true);

  const playSound = useCallback(async () => {
    try {
      if (!isPlayingRef.current && soundRef.current && soundEnabled) {
        isPlayingRef.current = true;
        await soundRef.current?.replayAsync?.();
        isPlayingRef.current = false;
      }
    } catch (error) {
      isPlayingRef.current = false;
      console.error('Error playing sound:', error);
    }
  }, [soundEnabled]);

  const loadSound = useCallback(async () => {
    const { sound } = await Audio.Sound.createAsync(soundFile, config);
    soundRef.current = sound;
    if (playOnMount) {
      playSound();
    }
  }, [playSound, config, playOnMount, soundFile]);

  const loadSoundRef = useRef(loadSound);
  loadSoundRef.current = loadSound;

  useEffect(() => {
    loadSoundRef.current();

    return () => {
      if (soundRef.current) {
        soundRef.current?.unloadAsync?.();
      }
    };
  }, [soundFile]);

  return { playSound };
};

export default useSound;
