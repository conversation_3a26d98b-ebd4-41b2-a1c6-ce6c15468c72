import { useCallback } from 'react';
import { Platform, Share } from 'react-native';

const useNativeUrlSharing = ({ url }) => {
  const handleShare = useCallback(
    async ({ label = '' } = EMPTY_OBJECT) => {
      const shareContent = Platform.select({
        ios: {
          message: `${label} ${url}`,
        },
        android: {
          message: `${label} ${url}`,
        },
      });
      try {
        await Share.share(shareContent);
      } catch (error) {
        console.error('Error sharing:', error);
      }
    },
    [url],
  );

  return {
    handleShare,
  };
};

export default useNativeUrlSharing;
