import { useCallback } from 'react';
import useLocalCache from 'core/hooks/useLocalCache';

const USER_CACHE_KEY = 'user-cache';

const useCurrentUserCache = () => {
  const { getData, setData } = useLocalCache(USER_CACHE_KEY);

  const updateUserCache = useCallback(({ user }) => setData(user), [setData]);

  return {
    getUserFromCache: getData,
    updateUserCache,
  };
};

export default useCurrentUserCache;
