import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    margin: 0,
    height: '100%',
    web: {
      position: 'sticky',
    },
    backgroundColor: dark.colors.background,
    justifyContent: 'flex-start',
    maxWidth: 175,
    paddingHorizontal: 4,
    paddingVertical: 20,
    borderRightColor: dark.colors.tertiary,
    borderRightWidth: 1,
  },
  primaryNavOptionsContainer: {
    flex: 1,
    gap: 12,
  },
  navItem: {
    height: 36,
    paddingHorizontal: 12,
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    borderRadius: 4,
  },
  hoveredNavItem: {
    // backgroundColor: withOpacity(dark.colors.secondary, 0.1),
  },
  selectedTabStyle: {
    borderColor: dark.colors.secondary,
    borderWidth: 1,
  },
  screenContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    gap: 6,
    width: '100%',
    borderRadius: 8,
  },
  iconContainer: {
    height: 20,
    width: 20,
    // backgroundColor: dark.colors.primary,
    // borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  modalItemText: {
    lineHeight: 16,
    color: dark.colors.textDark,
    fontSize: 11,
    fontFamily: 'Montserrat-400',
  },

  selectedTab: {
    width: 2,
    height: 28,
    borderRadius: 1,
    backgroundColor: dark.colors.secondary,
  },
  notSelectedTab: {
    width: 2,
    backgroundColor: 'transparent',
  },
  overlayStyle: {
    height: 70,
    width: 140,
    borderRadius: 8,
    padding: 0,
    position: 'absolute',
    backgroundColor: dark.colors.background,
  },
});

export default styles;
