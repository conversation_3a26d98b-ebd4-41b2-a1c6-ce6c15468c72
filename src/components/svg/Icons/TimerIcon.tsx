import React from 'react';
import Svg, { Path } from 'react-native-svg';

const TimerIcon = ({
  height = 24,
  width = 24,
  color = '#4B4B4B',
}: {
  height: number;
  width: number;
  color: string;
}) => (
  <Svg
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <Path
      d="M10 3C9.71667 3 9.47917 2.90417 9.2875 2.7125C9.09583 2.52083 9 2.28333 9 2C9 1.71667 9.09583 1.47917 9.2875 1.2875C9.47917 1.09583 9.71667 1 10 1H14C14.2833 1 14.5208 1.09583 14.7125 1.2875C14.9042 1.47917 15 1.71667 15 2C15 2.28333 14.9042 2.52083 14.7125 2.7125C14.5208 2.90417 14.2833 3 14 3H10ZM12 14C12.2833 14 12.5208 13.9042 12.7125 13.7125C12.9042 13.5208 13 13.2833 13 13V9C13 8.71667 12.9042 8.47917 12.7125 8.2875C12.5208 8.09583 12.2833 8 12 8C11.7167 8 11.4792 8.09583 11.2875 8.2875C11.0958 8.47917 11 8.71667 11 9V13C11 13.2833 11.0958 13.5208 11.2875 13.7125C11.4792 13.9042 11.7167 14 12 14ZM12 22C10.7667 22 9.60417 21.7625 8.5125 21.2875C7.42083 20.8125 6.46667 20.1667 5.65 19.35C4.83333 18.5333 4.1875 17.5792 3.7125 16.4875C3.2375 15.3958 3 14.2333 3 13C3 11.7667 3.2375 10.6042 3.7125 9.5125C4.1875 8.42083 4.83333 7.46667 5.65 6.65C6.46667 5.83333 7.42083 5.1875 8.5125 4.7125C9.60417 4.2375 10.7667 4 12 4C13.0333 4 14.025 4.16667 14.975 4.5C15.925 4.83333 16.8167 5.31667 17.65 5.95L18.35 5.25C18.5333 5.06667 18.7667 4.975 19.05 4.975C19.3333 4.975 19.5667 5.06667 19.75 5.25C19.9333 5.43333 20.025 5.66667 20.025 5.95C20.025 6.23333 19.9333 6.46667 19.75 6.65L19.05 7.35C19.6833 8.18333 20.1667 9.075 20.5 10.025C20.8333 10.975 21 11.9667 21 13C21 14.2333 20.7625 15.3958 20.2875 16.4875C19.8125 17.5792 19.1667 18.5333 18.35 19.35C17.5333 20.1667 16.5792 20.8125 15.4875 21.2875C14.3958 21.7625 13.2333 22 12 22Z"
      fill={color}
    />
  </Svg>
);

export default React.memo(TimerIcon);
