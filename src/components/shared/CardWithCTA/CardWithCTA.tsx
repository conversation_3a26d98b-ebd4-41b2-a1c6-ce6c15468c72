import { Image, Text, TouchableOpacity, View } from 'react-native';
import _isFunction from 'lodash/isFunction';
import _isNil from 'lodash/isNil';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import { CardWithCTAProps } from './types';
import styles from './CardWithCTA.style';

const CardWithCTA = (props: CardWithCTAProps) => {
  const {
    onButtonPress,
    buttonText,
    titleText,
    imageSource,
    renderButtonComponent,
  } = props;
  const { isMobile: isCompactDevice } = useMediaQuery();

  const renderActionButton = () => {
    if (_isFunction(renderButtonComponent) && !_isNil(renderButtonComponent)) {
      return renderButtonComponent();
    }
    return (
      <TouchableOpacity onPress={onButtonPress}>
        <Text style={styles.buttonText}>{buttonText}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <View
      style={[styles.container, !isCompactDevice && styles.expandedContainer]}
    >
      <View
        style={{
          flexDirection: 'row',
          gap: 10,
          paddingLeft: 16,
          justifyContent: 'flex-start',
          alignItems: 'center',
        }}
      >
        <View style={styles.imageContainer}>
          <Image source={imageSource} style={{ height: 15, width: 15 }} />
        </View>
        <View
          style={[
            { flex: 1, gap: 8, flexDirection: 'column' },
            !isCompactDevice && { flexDirection: 'row', alignItems: 'center' },
          ]}
        >
          <Text style={[styles.titleText]}>{titleText}</Text>
          {renderActionButton()}
        </View>
      </View>
    </View>
  );
};

export default CardWithCTA;
