import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    height: 36,
    backgroundColor: dark.colors.background,
    borderColor: dark.colors.tertiary,
    borderWidth: 0.5,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 6,
    flexDirection: 'row',
  },
  time: {
    fontSize: 14,
    lineHeight: 18,
    letterSpacing: 1,
    width: 30,
    textAlign: 'center',
    alignSelf: 'center',
    fontFamily: 'Montserrat-700',
    color: dark.colors.puzzle.primary,
  },
});

export default styles;
