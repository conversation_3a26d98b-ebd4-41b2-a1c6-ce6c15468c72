import React from 'react';
import { <PERSON><PERSON> } from 'modules/puzzles/utils/kenkenPuzzleGenerator';
import KenKenPuzzleQuestionActions from './components/KenKenPuzzleQuestionActions';
import KenKenPuzzleQuestionOptions from './components/KenKenPuzzleQuestionOptions';
import KenKenPuzzleQuestionTimer from './components/KenKenPuzzleQuestionTimer';
import KenKenPuzzleQuestionGrid from './components/KenKenPuzzleQuestionGrid';
import { KenKenPuzzleQuestionContext } from './context/context';
import useKenKenPuzzleContextState from './hooks/useKenKenPuzzleContextState';

export interface KenKenPuzzleQuestionProps {
  puzzle: Kenken;
  onSubmitPuzzle: ({ timeSpent }: { timeSpent: number }) => void;
  onWrongBoxFill?: () => void;
  onWrongCombination?: () => void;
  children: React.ReactNode;
  shouldCacheTime?: boolean;
}

const KenKenPuzzleQuestionRoot: React.FC<KenKenPuzzleQuestionProps> = ({
  puzzle,
  onSubmitPuzzle,
  onWrongBoxFill,
  onWrongCombination,
  shouldCacheTime = false,
  children,
}) => {
  const value = useKenKenPuzzleContextState({
    puzzle,
    onSubmitPuzzle,
    shouldCacheTime,
    onWrongBoxFill,
    onWrongCombination,
  });

  return (
    <KenKenPuzzleQuestionContext.Provider value={value}>
      {children}
    </KenKenPuzzleQuestionContext.Provider>
  );
};

const KenKenPuzzleQuestion = Object.assign(KenKenPuzzleQuestionRoot, {
  Grid: KenKenPuzzleQuestionGrid,
  Actions: KenKenPuzzleQuestionActions,
  Options: KenKenPuzzleQuestionOptions,
  Timer: KenKenPuzzleQuestionTimer,
});

export default KenKenPuzzleQuestion;
