import _join from 'lodash/join';
import _split from 'lodash/split';
import _get from 'lodash/get';
import _toString from 'lodash/toString';
import { PRACTICE_CATEGORIES } from '../pages/Practice/components/OperatorSelector/constants/practice';
import {
  EXPONENT_FIELDS_KEYS,
  MOD_FIELDS_KEYS,
  NTH_ROOT_FIELDS_KEYS,
} from '../constants/fieldKeys';
import { PRESET_CATEGORY } from '../../../core/constants/presets';

const getAdditionIdentifier = ({ presetConfig }) => {
  const { rows, digits, includeSubstraction } = presetConfig;
  let identifier = includeSubstraction
    ? `${PRESET_CATEGORY.ADDSUB}_`
    : `${PRESET_CATEGORY.ADD}_`;
  const digitsArray = Array(rows).fill(digits);
  identifier += _join(digitsArray, ',');
  return identifier;
};

const getMultiplicationIdentifier = ({ presetConfig }) => {
  const { noOfDigitsRow1, noOfDigitsRow2 } = presetConfig;
  return `${PRESET_CATEGORY.MULT}_${noOfDigitsRow1},${noOfDigitsRow2}`;
};

const getDivisonIdentifier = ({ presetConfig }) => {
  const { noOfDigitsRow1, noOfDigitsRow2 } = presetConfig;
  return `${PRESET_CATEGORY.DIV}_${noOfDigitsRow1},${noOfDigitsRow2}`;
};

const getFlashAnzanIdentifier = ({ presetConfig }) => {
  const { digits, includeSubstraction, flashSpeed, numberCount } = presetConfig;
  if (includeSubstraction) {
    return `${PRESET_CATEGORY.FLASH_ANZAN_WITH_SUB}_${digits},${flashSpeed},${numberCount}`;
  }
  return `${PRESET_CATEGORY.FLASH_ANZAN}_${digits},${flashSpeed},${numberCount}`;
};

const getNthRootIdentifier = ({ presetConfig }) => {
  const isPerfectPower = _get(
    presetConfig,
    [NTH_ROOT_FIELDS_KEYS.IS_PERFECT_POWER],
    false,
  );
  const nthRoot = _get(presetConfig, [NTH_ROOT_FIELDS_KEYS.ROOT], 2);
  const noOfDigits = _get(presetConfig, [NTH_ROOT_FIELDS_KEYS.NO_OF_DIGITS], 3);
  const roundOffDecimals = _get(
    presetConfig,
    [NTH_ROOT_FIELDS_KEYS.ROUND_OFF_TO_N_DECIMALS],
    2,
  );

  return `${PRESET_CATEGORY.ROOT}_${isPerfectPower ? '1' : '0'},${nthRoot},${noOfDigits},${isPerfectPower ? '0' : roundOffDecimals}`;
};

const getExponentIdentifier = ({ presetConfig }) => {
  const digitsInBase = _get(
    presetConfig,
    [EXPONENT_FIELDS_KEYS.DIGITS_IN_BASE],
    2,
  );
  const maxExponent = _get(
    presetConfig,
    [EXPONENT_FIELDS_KEYS.MAX_EXPONENT],
    3,
  );
  const decimalsInExponent = _get(
    presetConfig,
    [EXPONENT_FIELDS_KEYS.DECIMALS_IN_EXPONENT],
    2,
  );
  const precisionValue = _get(
    presetConfig,
    [EXPONENT_FIELDS_KEYS.PRECISION_IN_ANSWER],
    null,
  );

  // eslint-disable-next-line no-undef
  return `${PRESET_CATEGORY.EXPONENT}_${digitsInBase},${maxExponent},${decimalsInExponent},${_toString(precisionValue)}`;
};

const getModIdentifier = ({ presetConfig }) => {
  let digitsRow1 = _get(presetConfig, [MOD_FIELDS_KEYS.DIGITS_ROW_1], 2);
  let digitsRow2 = _get(presetConfig, [MOD_FIELDS_KEYS.DIGITS_ROW_2], 1);

  if (digitsRow1 < digitsRow2) {
    const tempDigitsR1 = digitsRow1;
    digitsRow1 = digitsRow2;
    digitsRow2 = tempDigitsR1;
  }

  return `${PRESET_CATEGORY.MOD}_${digitsRow1},${digitsRow2}`;
};

export const IDENTIFIER_FACTORY = {
  [PRACTICE_CATEGORIES.ADD_AND_SUBSTRACT]: getAdditionIdentifier,
  [PRACTICE_CATEGORIES.MULTIPLICATION]: getMultiplicationIdentifier,
  [PRACTICE_CATEGORIES.DIVISION]: getDivisonIdentifier,
  [PRACTICE_CATEGORIES.FLASH_ANZAN]: getFlashAnzanIdentifier,
  [PRACTICE_CATEGORIES.ROOT]: getNthRootIdentifier,
  [PRACTICE_CATEGORIES.EXPONENT]: getExponentIdentifier,
  [PRACTICE_CATEGORIES.MOD]: getModIdentifier,
};

export const getPresetCategoryFromIdentifier = ({ identifier }) =>
  _get(_split(identifier, '_'), [0]);
