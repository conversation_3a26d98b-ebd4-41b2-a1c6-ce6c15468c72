import { StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';

const createStyles = (isCompactMode) =>
  StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: isCompactMode ? 0 : 80,
    },
    innerContainer: {
      flex: 1,
      width: '100%',
      overflow: 'scroll',
      gap: 24,
      alignItems: 'center',
      justifyContent: 'center',
    },
    questionExpression: {
      fontSize: isCompactMode ? 14 : 14,
      fontFamily: 'Montserrat-500',
      color: 'white',
      textAlign: 'center',
      letterSpacing: 10,
    },
    expressionContainer: {
      gap: 12,
      justifyContent: 'center',
      alignItems: 'center',
    },
    expressionRow: {
      flexDirection: 'row',
      gap: 8,
      alignItems: 'center',
      justifyContent: 'center',
      // width: 120,
      // paddingRight: 36,
    },
    operatorContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
    },
    operator: {
      width: 16,
      // maxWidth: 24,
      fontSize: 14,
      color: 'white',
    },
    numberContainer: {
      flexDirection: 'row',
      justifyContent: 'flex-end',
    },
    trailingComponent: {
      justifyContent: 'center',
      flexDirection: 'row',
      alignItems: 'center',
      gap: 35,
    },
    questionCounter: {
      color: 'white',
      fontFamily: 'Montserrat-400',
      letterSpacing: 4,
    },
    timerContainer: {
      flexDirection: 'row',
      gap: 6,
      alignItems: 'center',
      width: 60,
    },
    timerText: {
      color: 'white',
      fontFamily: 'Montserrat-400',
      letterSpacing: 4,
    },
    digitBox: {
      width: 10,
      height: 16,
      alignItems: 'center',
      justifyContent: 'center',
    },
    digitContainer: {
      flexDirection: 'row',
      gap: 14,
      alignItems: 'center',
    },
    countdownContainer: {
      flex: 1,
      justifyContent: 'center',
      gap: 10,
      alignItems: 'center',
    },
    countdownText: {
      fontSize: 34,
      fontFamily: 'Montserrat-600',
      color: 'white',
    },
  });

const usePracticePlayPageStyles = () => {
  const { isMobile } = useMediaQuery();

  const styles = useMemo(() => createStyles(isMobile), [isMobile]);

  return styles;
};

export default usePracticePlayPageStyles;
