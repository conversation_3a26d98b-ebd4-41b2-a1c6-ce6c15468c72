import _map from 'lodash/map';
import React from 'react';
import { Image, ScrollView, Text, TouchableOpacity } from 'react-native';
import {
  GAME_CATEGORIES,
  VALID_GAME_CATEGORIES,
} from 'modules/home/<USER>/gameTypes';
import { router, useLocalSearchParams } from 'expo-router';
import _isEqual from 'lodash/isEqual';
import dark from 'core/constants/themes/dark';
import { GAME_CATEGORY_DETAILED_INFO } from 'core/constants/validUserRatingKeysAndInfo';
import _find from 'lodash/find';
import _isNil from 'lodash/isNil';
import styles from './ExpandedHomeLayoutTabBar.style';

const GameCategoryTabCard = ({ gameCategory }: { gameCategory: string }) => {
  const {
    gameCategory: currentActiveGameCategory = GAME_CATEGORIES.ALL,
  }: { gameCategory: string } = useLocalSearchParams();

  const isActive = _isEqual(currentActiveGameCategory, gameCategory);

  const onPress = () => {
    router.setParams({ gameCategory });
  };

  const iconUrl = _find(GAME_CATEGORY_DETAILED_INFO, {
    title: gameCategory,
  })?.iconUrl;

  return (
    <TouchableOpacity
      style={[
        styles.tabContainer,

        isActive && { borderColor: dark.colors.secondary },
      ]}
      onPress={onPress}
    >
      {!_isNil(iconUrl) && (
        <Image
          source={iconUrl}
          style={[styles.iconContainer, !isActive && { opacity: 0.5 }]}
        />
      )}
      <Text
        style={[
          styles.categoryText,
          isActive && { color: dark.colors.secondary },
        ]}
      >
        {gameCategory}
      </Text>
    </TouchableOpacity>
  );
};

const ExpandedHomeLayoutTabBar = () => (
  <ScrollView
    contentContainerStyle={styles.container}
    showsHorizontalScrollIndicator={false}
  >
    {_map(VALID_GAME_CATEGORIES, (gameCategory) => (
      <GameCategoryTabCard gameCategory={gameCategory} />
    ))}
  </ScrollView>
);

export default React.memo(ExpandedHomeLayoutTabBar);
