import vsIcon from '@/assets/images/icons/vs.png';
import flashIcon from '@/assets/images/icons/flash_on.png';
import groupPlayIcon from '@/assets/images/icons/group_play.png';
import playWthFriendIcon from '@/assets/images/icons/play_with_friend_home.png';
import { GAME_MODE } from 'modules/games/constants/gameModes';
import puzzleIcon from 'assets/images/icons/puzzle.png';
import { GAME_TYPES } from './gameTypes';

export const SHORTCUTS = [
  {
    key: GAME_TYPES.PLAY_ONLINE,
    title: 'MATH DUEL',
    subTitle: '1 MIN',
    isActive: true,
    weight: 1,
    highlightLabel: 'POPULAR',
    eventName: 'math duel',
    icon: vsIcon,
    route: '/search?timeLimit=1',
  },
  {
    key: GAME_TYPES.PUZZLE_DUELS,
    title: 'PUZZLE DUEL',
    subTitle: '2 MIN',
    eventName: 'puzzle duel',
    isActive: true,
    icon: puzzleIcon,
    weight: 1,
    // highlightLabel: 'New',
    route: `/puzzle/search-opponent`,
  },
  {
    key: GAME_TYPES.FLASH_ANZAN,
    title: 'ANZAN',
    subTitle: 'MEMORY',
    eventName: 'flash anzan duel',
    isActive: true,
    weight: 1,
    icon: flashIcon,
    route: `/play-time/instructions?gameType=${GAME_TYPES.FLASH_ANZAN}`,
  },
  {
    key: GAME_TYPES.PLAY_WITH_FRIEND,
    title: '1 VS 1',
    subTitle: 'FRIEND',
    eventName: 'play with friend',
    isActive: true,
    weight: 1,
    icon: playWthFriendIcon,
    route: `/games?gameMode=${GAME_MODE.VS_FRIEND}`,
  },
  {
    key: GAME_TYPES.GROUP_PLAY,
    title: 'GROUP PLAY',
    subTitle: 'BLITZ',
    eventName: 'group play',
    isActive: false,
    weight: 1,
    icon: groupPlayIcon,
    route: `/play-time/instructions?gameType=${GAME_TYPES.GROUP_PLAY}`,
  },
];
