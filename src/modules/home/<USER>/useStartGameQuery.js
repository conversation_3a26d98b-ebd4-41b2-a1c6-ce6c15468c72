import { gql, useMutation } from '@apollo/client'
import { useCallback } from 'react'
import _map from "lodash/map"
import { decryptJsonData } from 'core/utils/encryptions/decrypt'
import { GAME_FRAGMENT } from '@/src/core/graphql/fragments/game'

const START_GAME_MUTATION = gql`
    ${GAME_FRAGMENT}
    mutation StartGame($startGameInput: StartGameInput!) {
        startGame(startGameInput: $startGameInput) {
            ...CoreGameFields
        }
    }
`

const useStartGameQuery = () => {
    const [startGameMutation, { data = {}, loading, error }] =
        useMutation(START_GAME_MUTATION)

    const { startGameData } = data
    const { encryptedQuestions } = startGameData ?? EMPTY_OBJECT
    const questions = _map(encryptedQuestions, decryptJsonData);

    const startGame = useCallback(
        (gameId) => {
            const variables = {
                startGameInput: {
                    gameId,
                },
            }
            startGameMutation({ variables })
        },
        [startGameMutation]
    )

    return {
        startGameData: { ...startGameData, questions },
        startGame,
        startingGame: loading,
        error,
    }
}

export default useStartGameQuery
