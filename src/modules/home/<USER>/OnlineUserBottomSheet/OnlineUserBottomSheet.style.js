import { useMemo } from 'react'
import { StyleSheet } from 'react-native'
import useMediaQuery from 'core/hooks/useMediaQuery'
import dark from '../../../../core/constants/themes/dark'

const createStyles = (isCompactMode) =>
    StyleSheet.create({
        container: {
            paddingHorizontal: 5,
            paddingTop: 10,
            gap:16,
        },
        buttonRowContainer: {
            flexDirection: 'row',
            alignItems: 'center',
            gap: 12,
        },
        buttonOptionsContainer: {
            borderTopColor:dark.colors.tertiary,
            borderTopWidth:1,
            paddingTop: 16,
            gap: 20,
        },
        userImage: {
            height: 52,
            width: 52,
            borderRadius: 8,
            overflow: 'hidden',
            borderWidth: 0.5,
            borderColor: '#363636',
        },
        activityIndicator: {
            height: 12,
            width: 12,
            borderRadius: 6,
            borderWidth: 2,
            borderColor: dark.colors.gradientBackground,
            position: 'absolute',
            bottom: 0,
            right: 0,
            backgroundColor: dark.colors.inGameIndicator,
        },
        buttonText: {
            fontFamily: 'Montserrat-500',
            fontSize: 14,
            lineHeight: 20,
            color: 'white',
        },
        name: {
            fontFamily: 'Montserrat-600',
            fontSize: 15,
            lineHeight: 18,
            color: 'white',
        },
        userRating: {
            fontFamily: 'Montserrat-600',
            fontSize: 11,
            lineHeight: 13,
            color: 'white',
        },
        activityText: {
            fontFamily: 'Montserrat-600',
            fontSize: 8,
            lineHeight: 13,
            letterSpacing: 0.15,
            color: dark.colors.background,
        },
        activityContainer: {
            paddingHorizontal: 6,
            borderRadius: 8,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: dark.colors.inGameIndicator,
        },
        userInfoContainer: {
            gap: 3,
        },
        userImageWithIndicator: {
            height: 54,
            width: 54,
        },
        userInfoRow: {
            flexDirection: 'row',
            width:"100%",
            gap: 10,
        },
    })

const useOnlineUserBottomSheetStyles = () => {
    const { isMobile } = useMediaQuery()

    const styles = useMemo(() => createStyles(isMobile), [isMobile])

    return styles
}

export default useOnlineUserBottomSheetStyles
