import React, { useCallback, useEffect } from 'react';
import { Linking, Text, View } from 'react-native';
import { Image } from 'expo-image';
import streakFlame from 'assets/images/streakMilestones/streak-flame.png';
import hunderdDaysStreakFlame from 'assets/images/streakMilestones/100-days.png';
import {
  setStorageItemAsync,
  useStorageState,
} from 'core/hooks/useStorageState';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from 'core/readers/userReader';
import _toNumber from 'lodash/toNumber';
import {
  closeBottomSheet,
  openBottomSheet,
} from 'molecules/BottomSheet/BottomSheet';
import dark from 'core/constants/themes/dark';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';

const HUNDRED_DAYS_STREAK_MILESTONE_REACHED_KEYS =
  'streak-milestone-celebrated';
const MIN_STREAK_TO_CELEBRATE = 100;

const FORM_URL = 'https://forms.gle/iceetsPC4N3MYLx39';

const StreakMilestone = ({ closeBottomSheet }) => {
  const onPressOpenForm = useCallback(async () => {
    await setStorageItemAsync(
      HUNDRED_DAYS_STREAK_MILESTONE_REACHED_KEYS,
      'true',
    );
    closeBottomSheet();
    Linking.openURL(FORM_URL);
  }, [closeBottomSheet]);

  return (
    <View
      style={{
        maxWidth: 600,
        paddingTop: 32,
        gap: 32,
      }}
    >
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          paddingHorizontal: 16,
          gap: 16,
        }}
      >
        <Image source={streakFlame} style={{ height: 120, width: 120 }} />
        <Image
          source={hunderdDaysStreakFlame}
          style={{ height: 50, width: 200 }}
        />
        <Text
          style={{
            color: 'white',
            fontSize: 12,
            fontFamily: 'Montserrat-600',
            textAlign: 'center',
            letterSpacing: 0,
            lineHeight: 16,
          }}
        >
          Congratulations for completing 100 days streaks on Matiks, for
          celebrating this milestone, we would like to send some surprise.
          Please fill this form to share your address and other details with us.
        </Text>
      </View>
      <View
        style={{
          padding: 24,
          borderTopWidth: 0.5,
          borderTopColor: dark.colors.textDark,
        }}
      >
        <InteractiveSecondaryButton
          label="FILL THE FORM"
          borderColor={dark.colors.streak}
          onPress={onPressOpenForm}
          labelStyle={{
            color: dark.colors.streak,
            fontSize: 12,
            fontFamily: 'Montserrat-800',
          }}
        />
      </View>
    </View>
  );
};

const StreakMilestoneContainer = () => {
  const [streakCelebrated, setStreakCelebrated] = useStorageState(
    HUNDRED_DAYS_STREAK_MILESTONE_REACHED_KEYS,
  );

  const { user } = useSession();
  const userStreak = userReader.longestStreak(user);

  const showStreakMilestone = useCallback(() => {
    openBottomSheet({
      content: ({ closeBottomSheet }) => (
        <StreakMilestone closeBottomSheet={closeBottomSheet} />
      ),
      styles: {
        frame: {
          borderTopColor: dark.colors.streak,
        },
      },
    });
  }, []);

  useEffect(() => {
    let mounted = true;

    const handleStreak = async () => {
      const shouldCelebrate =
        _toNumber(userStreak) >= MIN_STREAK_TO_CELEBRATE &&
        streakCelebrated !== 'true';

      if (shouldCelebrate && mounted) {
        showStreakMilestone();
      }
    };

    handleStreak();

    return () => {
      mounted = false;
      closeBottomSheet();
    };
  }, [userStreak, streakCelebrated, showStreakMilestone]);

  return null;
};

export default React.memo(StreakMilestoneContainer);
