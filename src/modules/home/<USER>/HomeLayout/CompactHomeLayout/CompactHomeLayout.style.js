import { Platform, StyleSheet } from 'react-native';
import Dark from '@/src/core/constants/themes/dark';

const styles = StyleSheet.create({
  mobileContainer: {
    flex: 1,
    width: '100%',
    maxWidth: 420,
    alignItems: 'center',
    gap: 12,
  },
  gameTypesMainContainer: {
    flex: 1,
    width: '100%',
  },
  headingText: {
    letterSpacing: 1,
    fontSize: 10,
    lineHeight: 12,
    fontFamily: 'Montserrat-600',
    color: Dark.colors.textDark,
    opacity: 0.5,
    marginLeft: 16,
  },
  gameTypesContainer: {
    paddingBottom: 16,
    paddingHorizontal: 16,
    gap: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 12,
    marginBottom: '40%',
  },
  navbarProfileContainer: {
    flexDirection: 'row',
    gap: 4,
    alignItems: 'center',
  },
  playNowText: {
    fontSize: 10,
    color: Dark.colors.textDark,
    fontFamily: 'Montserrat-600',
    lineHeight: 12,
  },
  playTextContainer: {
    justifyContent: 'flex-start',
    width: '100%',
    paddingHorizontal: 16,
    marginTop: 5,
  },
  playNowContainer: {
    width: '100%',
    backgroundColor:
      Platform.OS === 'ios' ? Dark.colors.background : Dark.colors.primary,
    paddingVertical: 14,
    position: 'absolute',
    paddingHorizontal: 16,
    bottom: -1,
    left: 0,
    right: 0,
    zIndex: 5,
    height: 80,
    alignItems: 'center',
    justifyContent: 'center',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    borderTopWidth: 0.8,
    borderLeftWidth: 0.8,
    borderRightWidth: 0.8,
    borderColor: Dark.colors.tertiary,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: 16,
    alignItems: 'center',
    width: '100%',
  },
  textWithDivider: {
    marginBottom: 20,
  },
  playNowButton: {
    borderWidth: 0.8,
    borderColor: Dark.colors.secondary,
    borderRadius: 10,
    height: 50,
  },
  playNowButtonContainer: {
    flex: 1,
    width: '100%',
    minWidth: 200,
    justifyContent: 'center',
    alignItems: 'center',
    maxWidth: 400,
    height: 50,
    borderRadius: 12,
  },
  playNowButtonText: {
    fontSize: 12,
    fontFamily: 'Montserrat-800',
    lineHeight: 17,
    color: Dark.colors.textLight,
  },
  playNowBackground: {
    flex: 1,
    backgroundColor: Dark.colors.victoryColor,
  },
  container: {
    flex: 1,
  },
  hiddenContainer: {
    height: 120,
    backgroundColor: '#3498db',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1,
  },
  hiddenText: {
    fontSize: 18,
    color: 'white',
    fontWeight: 'bold',
  },
  hiddenSubText: {
    fontSize: 14,
    color: 'white',
    marginTop: 5,
  },

  scrollView: {
    flex: 1,
  },
});

export default styles;
