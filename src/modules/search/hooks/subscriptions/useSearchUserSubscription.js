import { useMemo } from 'react';
import { decryptJsonData } from 'core/utils/encryptions/decrypt';
import _map from 'lodash/map';

import { useWebSocketChannel } from 'core/hooks/useWebSocketChannel';
import { WEBSOCKET_CHANNELS } from 'core/services/WebSocketManager';
import { USER_EVENTS } from '../../constants/userEvents';
import { useSession } from '../../../auth/containers/AuthProvider';

const useSearchUserEventSubscription = () => {
  const { user, userId } = useSession();
  const channel = WEBSOCKET_CHANNELS.UserEvents(userId);

  const { message } = useWebSocketChannel(channel);

  const { event, game } = useMemo(() => {
    const payload = message?.event ?? EMPTY_OBJECT;
    const event = payload?.event;
    const game = payload?.game ?? EMPTY_OBJECT;

    switch (event) {
      case USER_EVENTS.USER_MATCHED: {
        const { encryptedQuestions } = game ?? EMPTY_OBJECT;
        const questions = _map(encryptedQuestions, decryptJsonData);
        const gameData = { ...game, questions };
        return { game: gameData, event };
      }
      case USER_EVENTS.SEARCH_TIMEOUT:
        return { game: null, event };
      default:
        return { game: payload?.game };
    }
  }, [message]);

  return { event, game };
};

export default useSearchUserEventSubscription;
