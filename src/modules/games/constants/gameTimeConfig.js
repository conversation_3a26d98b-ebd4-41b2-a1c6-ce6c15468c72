import { GAME_TYPES } from 'modules/home/<USER>/gameTypes';

export const DEFAULT_TIME_CONFIGS = [
  { key: 1, value: 60, label: '1-Min' },
  { key: 2, value: 120, label: '2-Min' },
  { key: 3, value: 180, label: '3-Min' },
  { key: 5, value: 300, label: '5-Min' },
];

export const ABILITY_DUELS_TIME_CONFIGS = [
  { key: 2, value: 120, label: '2-Min' },
  { key: 3, value: 180, label: '3-Min' },
  { key: 5, value: 300, label: '5-Min' },
  { key: 10, value: 600, label: '10-Min' },
];

export const PUZZLE_DUELS_TIME_CONFIGS = [
  { key: 3, value: 180, label: '3-Min' },
];

export const GAME_TYPES_TIME_CONFIG = {
  [GAME_TYPES.PLAY_ONLINE]: DEFAULT_TIME_CONFIGS,
  [GAME_TYPES.FASTEST_FINGER]: DEFAULT_TIME_CONFIGS,

  [GAME_TYPES.ABILITY_DUELS]: ABILITY_DUELS_TIME_CONFIGS,
  [GAME_TYPES.FLASH_ANZAN]: [],
  [GAME_TYPES.PUZZLE_DUELS]: PUZZLE_DUELS_TIME_CONFIGS,
};
