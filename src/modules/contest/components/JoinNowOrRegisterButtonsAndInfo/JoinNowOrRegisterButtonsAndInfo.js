import React, { useCallback, useMemo, useState } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import groupIcon from 'assets/images/group.png';
import timerIcon from 'assets/images/timer.png';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import _isEmpty from 'lodash/isEmpty';
import useCountDownTimer from 'core/hooks/useCountDownTimer';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from 'core/readers/userReader';
import styles from './JoinNowOrRegisterButtonsAndInfo.style';
import FormModal from '../FormModal';
import JoinNowOrRegisterButtonsShimmer from './JoinNowOrRegisterButtonsShimmer';
import dark from '../../../../core/constants/themes/dark';
import useContestContext from '../../hooks/useContestContext';

const JoinNowOrRegisterButtonsAndInfos = () => {
  const [isModalVisible, setModalVisible] = useState(false);

  const {
    contestDetails,
    refetch,
    loading,
    isLive,
    isAboutToStart,
    formFields,
    isSubmitting,
    hasEnded,
    hasCompleted,
    hasUserRegistered,
    handleJoinOrSubmitButtonPress,
    onPressRegisterGuestUser,
    handleSubmit,
    onPressUnRegister,
    isGuest,
  } = useContestContext();

  const onContestRegistrationSuccess = useCallback(() => {
    setModalVisible(false);
    refetch?.();
  }, [refetch]);

  const handleFormSubmit = useCallback((formData) => {
    handleSubmit(formData);
    setModalVisible(false);
  }, [handleSubmit]);

  const { startTime, endTime } = contestDetails ?? {};

  const { user } = useSession();
  const canParticipateInTournament =
    userReader.canParticipateInTournaments(user);

  const shouldShowRegisterButton = !hasEnded && !hasUserRegistered;
  const contestIsAboutToStartOrLiveAndUserIsRegistered =
    (hasUserRegistered && isLive && !hasEnded) || (!isLive && isAboutToStart);
  const canUserWithdrawRegistration = hasUserRegistered && !hasEnded && !isLive;
  const contestEndedAndUserIsRegistered = hasUserRegistered && hasEnded;
  const showLockedRegisterButtonForGuest =
    !canParticipateInTournament && !hasEnded;
  const contestIsLiveAndNotEnded = !hasEnded && isLive;

  const renderJoinNowOrRegisterButton = useMemo(() => {
    if (hasCompleted || hasEnded) {
      return null;
    }

    if (showLockedRegisterButtonForGuest) {
      return (
        <TouchableOpacity
          style={styles.lockedRegisterButton}
          onPress={onPressRegisterGuestUser}
        >
          <FontAwesome name="lock" size={20} color={dark.colors.text} />
          <Text style={styles.lockedRegisterText}>Register now</Text>
        </TouchableOpacity>
      );
    }

    if (shouldShowRegisterButton) {
      return (
        <TouchableOpacity
          onPress={handleJoinOrSubmitButtonPress}
          style={styles.registerButton}
        >
          <Text style={styles.registerText}>
            {isSubmitting ? 'Registering...' : 'Register Now'}
          </Text>
        </TouchableOpacity>
      );
    }

    if (contestIsAboutToStartOrLiveAndUserIsRegistered) {
      return (
        <TouchableOpacity
          testID='join-now-button'
          onPress={handleJoinOrSubmitButtonPress}
          style={styles.registerButton}
        >
          <Text style={styles.registerText}>
            {isLive ? 'Join Now' : 'Join Waiting Room'}
          </Text>
        </TouchableOpacity>
      );
    }

    if (contestEndedAndUserIsRegistered) {
      return (
        <TouchableOpacity
          style={styles.registerButton}
          onPress={handleJoinOrSubmitButtonPress}
        >
          <Text style={styles.registerText}>My Result</Text>
        </TouchableOpacity>
      );
    }

    if (canUserWithdrawRegistration) {
      return (
        <TouchableOpacity
          style={styles.unRegisterButton}
          onPress={onPressUnRegister}
        >
          <Text style={styles.unregisterText}>Unregister</Text>
        </TouchableOpacity>
      );
    }

    return null;
  }, [
    isGuest,
    contestEndedAndUserIsRegistered,
    contestIsAboutToStartOrLiveAndUserIsRegistered,
    shouldShowRegisterButton,
    canUserWithdrawRegistration,
    isLive,
    isSubmitting,
    handleJoinOrSubmitButtonPress,
  ]);

  const ContestTimer = useCallback(() => {
    const timeLeftToStart = useCountDownTimer({ targetTime: startTime });
    const timeLeftToEnd = useCountDownTimer({ targetTime: endTime });
    const contestStartOrEndTimeLeftLabel = contestIsLiveAndNotEnded
      ? 'Contest Ends in '
      : 'Contest Starts in ';
    const timeToShow = contestIsLiveAndNotEnded
      ? timeLeftToEnd
      : timeLeftToStart;

    return (
      <View style={styles.infoItem}>
        <View style={styles.iconContainer}>
          <Image source={timerIcon} style={styles.iconStyle} />
        </View>
        <View style={styles.info}>
          <Text style={styles.infoText}>{contestStartOrEndTimeLeftLabel}</Text>
          <Text style={styles.infoNumber}>{timeToShow}</Text>
        </View>
      </View>
    );
  }, [startTime, endTime, contestIsLiveAndNotEnded]);

  const RegistrationInfo = useMemo(
    () => (
      <View style={styles.infoContainer}>
        <View style={styles.infoItem}>
          <View style={styles.iconContainer}>
            <Image source={groupIcon} style={styles.iconStyle} />
          </View>
          <View style={styles.info}>
            <Text style={styles.infoText}>Registered</Text>
            <Text style={styles.infoNumber}>
              {contestDetails?.registrationCount}
            </Text>
          </View>
        </View>
        <ContestTimer />
        <View style={{ marginTop: 20, width: '100%' }}>
          {renderJoinNowOrRegisterButton}
        </View>
        {!_isEmpty(formFields) && (
          <FormModal
            modalVisible={isModalVisible}
            setModalVisible={setModalVisible}
            fields={formFields}
            onSubmit={handleFormSubmit}
            isLoading={isSubmitting}
          />
        )}
      </View>
    ),
    [
      contestDetails,
      renderJoinNowOrRegisterButton,
      isModalVisible,
      formFields,
      isSubmitting,
      isLive,
      hasEnded,
    ],
  );

  const EligibilityCriteria = useMemo(
    () => (
      <View style={styles.infoContainer}>
        <Text style={styles.eligibilityTextHeading}>Eligibility Criteria</Text>
        <View style={styles.infoItem}>
          <View style={styles.iconContainer}>
            <Text style={{ color: dark.colors.textDark }}>Ꝏ</Text>
          </View>
          <Text style={styles.eligibilityText}>
            Everyone is eligible to participate
          </Text>
        </View>
      </View>
    ),
    [],
  );

  if (hasEnded) {
    return null;
  }

  if (loading) {
    return <JoinNowOrRegisterButtonsShimmer />;
  }

  return (
    <View style={{ gap: 16 }}>
      {RegistrationInfo}
      {/* {EligibilityCriteria} */}
    </View>
  );
};

export default JoinNowOrRegisterButtonsAndInfos;
