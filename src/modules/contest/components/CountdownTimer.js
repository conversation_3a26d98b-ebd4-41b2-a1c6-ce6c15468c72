import React, { useState, useEffect, useCallback } from 'react'
import { View, Text } from 'react-native'
import styles from './ContestDetail.style'

const CountdownTimer = ({ targetDate, showOnlyDays = false, prefix = 'Starts in' }) => {
    const targetTime = new Date(targetDate).getTime()

    const [timeLeft, setTimeLeft] = useState(calculateTimeLeft())

    function calculateTimeLeft() {
        const difference = targetTime - new Date().getTime()
        if (difference <= 0) {
            return { days: 0, hours: 0, minutes: 0, seconds: 0 }
        }

        return {
            days: Math.floor(difference / (1000 * 60 * 60 * 24)),
            hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
            minutes: Math.floor((difference / 1000 / 60) % 60),
            seconds: Math.floor((difference / 1000) % 60),
        }
    }

    useEffect(() => {
        const timer = setInterval(() => {
            setTimeLeft(calculateTimeLeft())
        }, 1000)

        return () => clearInterval(timer)
    }, [targetTime])

    const formatTime = (time) => {
        return time < 10 ? `0${time}` : time
    }

    if (showOnlyDays) {
        return (
            <Text style={styles.daysLeftDetails}>
                {timeLeft.days > 0
                    ? ` ${formatTime(timeLeft.days)} days`
                    : timeLeft.hours > 0
                      ? ` ${formatTime(timeLeft.hours)} hrs`
                      : timeLeft.minutes > 0
                        ? ` ${formatTime(timeLeft.minutes)} mins`
                        : timeLeft.seconds > 0
                          ? ` ${formatTime(timeLeft.seconds)} secs`
                          : null}
            </Text>
        )
    }

    const label =
        timeLeft.days > 0
            ? `${prefix} ${timeLeft.days} days`
            : `${prefix} ${formatTime(timeLeft.hours)}:${formatTime(timeLeft.minutes)}:${formatTime(timeLeft.seconds)}`

    return (
        <View style={styles.startsInBox}>
            <Text style={styles.startsInText}>{label}</Text>
        </View>
    )
}

export default React.memo(CountdownTimer)
