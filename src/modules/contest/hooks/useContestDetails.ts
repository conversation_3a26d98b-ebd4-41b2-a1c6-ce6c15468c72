/**
 * Hook for fetching contest details
 * This hook provides functionality for fetching and decrypting contest details
 */

import _isEmpty from 'lodash/isEmpty';
import { ApolloError, gql, useQuery } from '@apollo/client';
import { decryptJsonData } from 'core/utils/encryptions/decrypt';
import _map from 'lodash/map';
import _get from 'lodash/get';
import { CONTEST_FRAGMENT } from 'core/graphql/fragments/contest';
import { Contest, ContestQuestion } from '../types/Contest';

/**
 * GraphQL query for fetching contest details
 */
const CONTEST_DETAILS_QUERY = gql`
  ${CONTEST_FRAGMENT}
  query GetContestQuery($contestId: ID!) {
    getContestById(contestId: $contestId) {
      ...ContestFields
    }
  }
`;

interface UseContestDetailsProps {
  contestId: string;
}

interface UseContestDetailsResult {
  loading: boolean;
  error?: ApolloError;
  refetch: () => Promise<any>;
  contestDetails?: Contest & { questions?: ContestQuestion[] };
}

/**
 * Hook for fetching contest details
 * @param {UseContestDetailsProps} props - Hook props
 * @returns {UseContestDetailsResult} - Contest details result
 */
const useContestDetails = ({
  contestId,
}: UseContestDetailsProps): UseContestDetailsResult => {
  const { data, loading, error, refetch } = useQuery(CONTEST_DETAILS_QUERY, {
    variables: { contestId },
    fetchPolicy: 'cache-and-network',
    skip: !contestId,
  });

  const contestDetails = _get(data, 'getContestById');

  if (loading || error || _isEmpty(contestDetails)) {
    return {
      loading,
      error,
      refetch,
      contestDetails: undefined,
    };
  }

  const { encryptedQuestions } = contestDetails;
  const questions = _map(encryptedQuestions, (encryptedQuestion) => {
    try {
      return decryptJsonData(encryptedQuestion);
    } catch (e) {
      console.error('Error decrypting question:', e);
      return null;
    }
  }).filter(Boolean) as ContestQuestion[];

  return {
    loading,
    error,
    refetch,
    contestDetails: {
      ...contestDetails,
      questions,
    },
  };
};

export default useContestDetails;
