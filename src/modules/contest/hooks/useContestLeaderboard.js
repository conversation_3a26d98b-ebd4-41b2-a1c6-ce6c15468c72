/**
 * Hook for fetching and managing contest leaderboard data
 * This hook provides functionality for fetching and paginating leaderboard data
 */

import _size from 'lodash/size';
import _isEmpty from 'lodash/isEmpty';
import { useCallback, useMemo } from 'react';
import useContestLeaderboardQuery from './queries/useContestLeaderboardQuery';
import { Contest, LeaderboardEntry } from '../types/Contest';
import { LEADERBOARD_CONSTANTS } from '../constants/contestConstants';

interface UseContestLeaderboardProps {
  contest: Contest;
  pageSize?: number;
  includeUserRank?: boolean;
}

interface UseContestLeaderboardResult {
  loading: boolean;
  error?: any;
  fetchMore: () => Promise<any>;
  loadingMore: boolean;
  hasMore: boolean;
  participants: LeaderboardEntry[];
  totalParticipants: number;
  userRank?: number;
  refetch: () => Promise<any>;
}

/**
 * Hook for fetching and managing contest leaderboard data
 * @param {UseContestLeaderboardProps} props - Hook props
 * @returns {UseContestLeaderboardResult} - Contest leaderboard result
 */
const useContestLeaderboard = ({
  contest,
  pageSize = LEADERBOARD_CONSTANTS.DEFAULT_PAGE_SIZE,
  includeUserRank = false,
}: UseContestLeaderboardProps): UseContestLeaderboardResult => {
  // Fetch leaderboard data
  const {
    loading,
    error,
    participants,
    totalParticipants,
    fetchMore,
    refetch,
    userRank
  } = useContestLeaderboardQuery({
    contest,
    pageSize,
    includeUserRank
  });

  // Check if there are more participants to load
  const hasMore = useMemo(() => {
    return _size(participants) < totalParticipants;
  }, [participants, totalParticipants]);

  // Check if we're loading more participants
  const loadingMore = useMemo(() => {
    return loading && !_isEmpty(participants);
  }, [loading, participants]);

  return {
    loading,
    error,
    fetchMore,
    loadingMore,
    hasMore,
    participants,
    totalParticipants,
    userRank,
    refetch,
  };
};

export default useContestLeaderboard;
