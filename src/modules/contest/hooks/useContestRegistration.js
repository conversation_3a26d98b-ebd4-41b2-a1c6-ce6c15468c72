import { useCallback, useState } from 'react';

import { showToast, TOAST_TYPE } from 'molecules/Toast'

import useRegisterForContest from './mutations/useRegisterForContest'
import useUnRegisterFromContest from './mutations/useUnregisterFromContest';
import Analytics from "../../../core/analytics";
import {ANALYTICS_EVENTS} from "../../../core/analytics/const";
import {getContestPropertiesToTrack} from "../utils/contestEvents";
import contestReader from "../readers/contestReader";
import {PAGE_NAME_KEY, PAGE_NAMES} from "../../../core/constants/pageNames";

const useContestRegistration = ({ contest, refetch, onContestRegistrationSuccess, onContestRegistrationFailure  }) => {
    const [isSubmitting, setIsSubmitting] = useState(false);

    const contestId = contestReader.id(contest);

    const { registerForContest } = useRegisterForContest();
    const { unregisterFromContest } = useUnRegisterFromContest();

    const handleSubmit = useCallback(
        async (formData) => {
            setIsSubmitting(true)
            try {
                const result = await registerForContest({
                    contestId,
                    formData,
                });
                Analytics.track(ANALYTICS_EVENTS.CONTEST.CONTEST_REGISTRATION_SUBMITTED, {
                    ...getContestPropertiesToTrack({ contest }),
                    [PAGE_NAME_KEY]: PAGE_NAMES.CONTEST_DETAILS,
                })
                if (result.data.registerForContest) {
                    onContestRegistrationSuccess?.();
                    showToast({
                        type: TOAST_TYPE.SUCCESS,
                        description:
                            'Successfully registered for the contest!',
                    })
                } else {
                    throw new Error(result.data.registerForContest.message)
                }
            } catch (error) {
                onContestRegistrationFailure?.();
                showToast({
                    type: TOAST_TYPE.ERROR,
                    description: `Registration failed: ${error.message}`,
                })
            } finally {
                setIsSubmitting(false)
            }
        },
        [contest, registerForContest, onContestRegistrationSuccess, onContestRegistrationFailure]
    )

    const onPressUnRegister = useCallback(async () => {
        try {
            const result = await unregisterFromContest({ contestId })
            Analytics.track(ANALYTICS_EVENTS.CONTEST.CLICKED_ON_UNREGISTER_FROM_CONTEST, {
                ...getContestPropertiesToTrack({ contest }),
                [PAGE_NAME_KEY]: PAGE_NAMES.CONTEST_DETAILS,
            })
            if (result.data.unregisterFromContest) {
                showToast({
                    type: TOAST_TYPE.SUCCESS,
                    description:
                        'Successfully removed registration from the contest!',
                })
                refetch()
            } else {
                throw new Error(
                    result.data.unregisterFromContest?.errors?.[0]?.message
                )
            }
        } catch (error) {
            showToast({
                type: TOAST_TYPE.ERROR,
                description: `Registration failed: ${error.message}`,
            })
        }
    }, [contest, unregisterFromContest, refetch])

    return {
        isSubmitting,
        handleSubmit,
        onPressUnRegister
    }
}

export default useContestRegistration;