import { useQuery, gql } from '@apollo/client'
import { useCallback } from 'react'
import { CONTEST_FRAGMENT } from '../../../core/graphql/fragments/contest'

const FEATURED_CONTESTS_QUERY = gql`
    ${CONTEST_FRAGMENT}
    query FeaturedContestQuery {
        getFeaturedContests {
            ...ContestFields
        }
    }
`

const useFetchFeaturedContests = () => {
    const { data, loading, error, refetch } = useQuery(
        FEATURED_CONTESTS_QUERY,
        {
            fetchPolicy: 'cache-first',
        }
    )

    const handleRefetch = useCallback(() => {
        refetch()
    }, [refetch])

    return {
        featuredContests: data?.getFeaturedContests || [],
        loading,
        error,
        refetch: handleRefetch,
    }
}

export default useFetchFeaturedContests
