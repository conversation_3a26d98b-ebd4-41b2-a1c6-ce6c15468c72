import { useCallback } from 'react'
import { gql, useQuery } from '@apollo/client'
import _size from 'lodash/size'
import _uniqBy from 'lodash/uniqBy'

import { USER_PUBLIC_DETAIL_FRAGMENT } from 'core/graphql/fragments/userPublicDetail'
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset'

const CONTEST_LEADER_BOARD_QUERY = gql`
    ${USER_PUBLIC_DETAIL_FRAGMENT}
    query GetContestLeaderboard($contestId: ID!, $pageNumber: Int, $pageSize: Int) {
        getContestLeaderboard(
            contestId: $contestId
            pageNumber: $pageNumber
            pageSize: $pageSize
        ) {
            participants {
                user {
                    ...UserPublicDetailFields
                  
                }
                registrationData {
                    name
                    values
                }
                score
                lastSubmissionTime
                rank
                startTime
                correctSubmission
                incorrectSubmission
            }
            totalParticipants
        }
    }
`

const DEFAULT_PAGE_SIZE = 20

const useContestLeaderBoardQuery = ({
    contest,
    pageSize = DEFAULT_PAGE_SIZE,
}) => {
    const { _id: contestId, endTime, startTime } = contest

    const endDateTime = new Date(endTime)
    const startDateTime = new Date(startTime)

    const isLiveContest = endDateTime > getCurrentTimeWithOffset() && startDateTime < getCurrentTimeWithOffset()

    const { loading, error, data, fetchMore } = useQuery(
        CONTEST_LEADER_BOARD_QUERY,
        {
            // notifyOnNetworkStatusChange: true,
            // fetchPolicy: isLiveContest ? 'cache-and-network' : 'cache-first',
            variables: {
                pageNumber: 1,
                contestId,
                pageSize,
            },
        }
    )

    const { participants, totalParticipants } =
        data?.getContestLeaderboard || EMPTY_OBJECT

    const loadMoreParticipants = useCallback(
        () =>
            fetchMore({
                variables: {
                    pageNumber: Math.ceil(_size(participants) / pageSize) + 1,
                },
                updateQuery: (prev, { fetchMoreResult }) => {
                    if (!fetchMoreResult) return prev
                    return {
                        getContestLeaderboard: {
                            ...prev.getContestLeaderboard,
                            participants: _uniqBy(
                                [
                                    ...prev.getContestLeaderboard.participants,
                                    ...fetchMoreResult.getContestLeaderboard
                                        .participants,
                                ],
                                (participant) => participant?.user?._id
                            ),
                        },
                    }
                },
            }),
        [participants, fetchMore]
    )

    return {
        loading,
        error,
        participants,
        totalParticipants,
        fetchMore: loadMoreParticipants,
    }
}

export default useContestLeaderBoardQuery
