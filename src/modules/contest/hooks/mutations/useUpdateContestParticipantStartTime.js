import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';

const UPDATE_CONTEST_PARTICIPANT_START_TIME_QUERY = gql`
  mutation UpdateContestParticipantStartTime($contestId: ID!) {
    updateContestParticipantStartTime(contestId: $contestId)
  }
`;

const useUpdateContestParticipantStartTime = () => {
  const [updateContestParticipantStartTimeQuery] = useMutation(
    UPDATE_CONTEST_PARTICIPANT_START_TIME_QUERY,
  );

  const updateContestParticipantStartTime = useCallback(
    async ({ contestId }) => {
      try {
        Analytics.track(ANALYTICS_EVENTS.CONTEST.CONTEST_STARTED, {
          contestId,
        });
        return await updateContestParticipantStartTimeQuery({
          variables: { contestId },
        });
      } catch (error) {
        return error.message;
      }
    },
    [updateContestParticipantStartTimeQuery],
  );

  return { updateContestParticipantStartTime };
};

export default useUpdateContestParticipantStartTime;
