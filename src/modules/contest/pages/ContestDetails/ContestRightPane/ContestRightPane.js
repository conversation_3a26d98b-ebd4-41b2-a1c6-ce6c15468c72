/**
 * ContestDetailsRightPane Component
 * This component displays the right pane of the contest details page
 */

import React from 'react';
import { View } from "react-native";
import JoinNowOrRegisterButtonsAndInfos from "../../../components/JoinNowOrRegisterButtonsAndInfo";

/**
 * ContestDetailsRightPane Component
 */
const ContestDetailsRightPane = () => {
    return (
        <View style={{ width: '25%', justifyContent: 'flex-start' }}>
            <JoinNowOrRegisterButtonsAndInfos />
        </View>
    );
};

export default React.memo(ContestDetailsRightPane);