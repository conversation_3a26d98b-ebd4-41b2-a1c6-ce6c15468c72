/**
 * ContestDetailInfo Component
 * Displays detailed information about a contest
 */

import React, { useMemo } from 'react';
import { View, Text } from 'react-native';
import useContestDetails from '../../../hooks/useContestDetails';
import ContestRegistrationInfo from '../../../components/ContestRegistrationInfo';
import dark from '../../../../../core/constants/themes/dark';
import ContestDetailBase from '../../../components/ContestDetailBase';
import { formatContestDuration } from '../../../utils/contest';

const EMPTY_OBJECT = {};

/**
 * ContestDetailInfo Component
 * @param {Object} props - Component props
 * @param {string} props.contestId - ID of the contest to display
 */
const ContestDetailInfo = ({ contestId }) => {
    const { loading, error, contestDetails } = useContestDetails({
        contestId,
    });

    const renderInfoRow = useMemo(() => {
        return (contest) => {
            const { startTime, endTime, contestDuration } = contest;

            return (
                <View style={{ marginBottom: 16 }}>
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 16 }}>
                        <View style={{ flex: 1 }}>
                            <Text style={{ fontSize: 12, color: dark.colors.textSecondary, marginBottom: 4 }}>Contest open time</Text>
                            <Text style={{ fontSize: 14, color: dark.colors.textPrimary, fontWeight: '500' }}>
                                {new Date(startTime).toLocaleString()}
                            </Text>
                        </View>
                        <View style={{ flex: 1 }}>
                            <Text style={{ fontSize: 12, color: dark.colors.textSecondary, marginBottom: 4 }}>Contest close time</Text>
                            <Text style={{ fontSize: 14, color: dark.colors.textPrimary, fontWeight: '500' }}>
                                {new Date(endTime).toLocaleString()}
                            </Text>
                        </View>
                        <View style={{ flex: 1 }}>
                            <Text style={{ fontSize: 12, color: dark.colors.textSecondary, marginBottom: 4 }}>Duration</Text>
                            <Text style={{ fontSize: 14, color: dark.colors.textPrimary, fontWeight: '500' }}>
                                {formatContestDuration(contestDuration)}
                            </Text>
                        </View>
                    </View>
                </View>
            );
        };
    }, []);

    const renderFooter = useMemo(() => {
        return (contest) => (
            <View style={{ marginTop: 16 }}>
                <ContestRegistrationInfo contest={contest} />
            </View>
        );
    }, []);

    return (
        <ContestDetailBase
            contestDetails={contestDetails}
            loading={loading}
            error={error}
            renderInfoRow={renderInfoRow}
            renderFooter={renderFooter}
            gradientColors={dark.colors.contestLogoBgGradient}
        />
    );
};

export default React.memo(ContestDetailInfo);
