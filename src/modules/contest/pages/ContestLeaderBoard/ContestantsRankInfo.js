import React, { useCallback } from 'react'
import { View, Text } from 'react-native'
import _isEmpty from 'lodash/isEmpty'
import LeaderBoardHeader from './components/LeaderBoardHeader'
import styles from './ContestLeaderBoard.style'
import ContestLeaderboardRow from './components/ContestLeaderboardRow/ContestLeaderboardRow'
import UserScoreInContestRow from './components/UserScoreInContestRow'
import PaginatedList from '../../../../components/shared/PaginatedList'
import useGetContestLeaderboard from '../../hooks/useGetContestLeaderboard'
import { CONTEST_STATUS } from '../../constants'
import PlaceholderRow from '../../../../components/shared/PlaceholderRow'

const PAGE_SIZE = 50
const ContestantsRankInfo = React.memo(({ contest }) => {
    const isContestLive = contest.status === CONTEST_STATUS.ONGOING;
    const { fetchLeaderboard } = useGetContestLeaderboard({ contestId: contest?._id, isLive: isContestLive, pageSize: PAGE_SIZE });

    const fetchData = useCallback(async ({ pageNumber }) => {
        const response = await fetchLeaderboard({ pageNumber });
        const { data } = response;
        const { getContestLeaderboard: participantsObject } = data;
        const { participants, totalParticipants } = participantsObject;
        return { data: participants, totalItems: totalParticipants };
    }, []);

    const renderHeader = ({ page, data }) => {
        return (
            <View style={{ width: '100%', paddingHorizontal: 14, }}>
                <LeaderBoardHeader headerStyle={[styles.headerContainer]} />
                {page === 1 && (<UserScoreInContestRow contest={contest} />)}
            </View>
        )
    }

    const renderRow = useCallback(({ item }) => (
        <View style={{ marginHorizontal: 15 }}>
            <ContestLeaderboardRow participantSubmission={item} contest={contest} />
            {renderSeparator()}
        </View>
    ), [contest])

    const renderSeparator = useCallback(
        () => <View style={styles.separator} />,
        []
    )

    const renderPlaceholderRow = useCallback(() => {
        return (<View style={{ marginHorizontal: 15 }}>
            <PlaceholderRow />
            <PlaceholderRow />
            <PlaceholderRow />
        </View>)
    }, [])


    const emptyLeaderboardComponent = useCallback(() => {
        return (<View style={styles.emptyLeaderboardContainer}>
            <Text style={styles.emptyLeaderboardLabel}>
                Leaderboard is empty
            </Text>
        </View>)
    }, [])

    return (
        <View style={styles.container}>
            <PaginatedList
                placeholderComponent={renderPlaceholderRow}
                fetchData={fetchData}
                renderItem={renderRow}
                renderHeader={renderHeader}
                emptyListComponent={emptyLeaderboardComponent}
                makeHeaderScrollable={false}
                pageSize={PAGE_SIZE}
                keyExtractor={(item, index) => `${item.user?._id.toString()} - ${index}`}
                contentContainerStyle={styles.list}
                listFooterComponent={<View style={{ height: 80 }}></View>}
            />
        </View>
    )
})


export default React.memo(ContestantsRankInfo)
