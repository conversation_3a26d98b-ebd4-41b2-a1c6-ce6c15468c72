/**
 * Contest Utility Functions
 * This file contains utility functions for contest-related operations
 */

import _isNil from 'lodash/isNil';
import _size from 'lodash/size';
import _isEmpty from 'lodash/isEmpty';
import _head from "lodash/head";
import { Contest, UserParticipation } from '../types/Contest';
import getCurrentTimeWithOffset from '../../../core/utils/getCurrentTimeWithOffset';

/**
 * Calculate the time spent by a user in a contest
 * @param {Object} params - The parameters
 * @param {Contest} params.contest - The contest object
 * @param {UserParticipation} params.participantSubmission - The participant submission object
 * @returns {number} - The time spent in milliseconds
 */
export const getTimeSpentByUser = ({ contest, participantSubmission }) => {
    if (_isEmpty(contest) || _isEmpty(participantSubmission)) {
        return 0;
    }

    const { startTime: contestStartTime, endTime: contestEndTime, contestDuration } = contest;
    const contestStartTimeStamp = new Date(contestStartTime).getTime();
    const contestEndTimeStamp = new Date(contestEndTime).getTime();

    const { startTime: userStartTime, lastSubmissionTime, correctSubmission: correctSubmissionCount = 0 } = participantSubmission;

    const hasUserSubmittedAllTheAnswers = correctSubmissionCount === _size(contest.questions);

    if (_isNil(lastSubmissionTime)) {
        return 0;
    }

    const userStartTimeStamp = new Date(userStartTime ?? contestStartTime).getTime();
    const lastSubmissionTimeStamp = new Date(lastSubmissionTime).getTime();

    const startTime = Math.max(contestStartTimeStamp, userStartTimeStamp);

    const effectiveLastSubmissionTimeStamp = Math.min(lastSubmissionTimeStamp, contestEndTimeStamp);

    const endTime = hasUserSubmittedAllTheAnswers
        ? Math.min(effectiveLastSubmissionTimeStamp, contestEndTimeStamp)
        : Math.min(startTime + contestDuration * 1000, Math.max(effectiveLastSubmissionTimeStamp, contestEndTimeStamp));

    if (lastSubmissionTimeStamp > endTime && startTime > endTime) {
        return Math.min(lastSubmissionTimeStamp - startTime, contestDuration * 1000)
    }

    return endTime > startTime ? (endTime - startTime) : 0;
};

/**
 * Get the start time of a contest for a user
 * @param {Object} params - The parameters
 * @param {Contest} params.contest - The contest object
 * @param {UserParticipation} params.userSubmission - The user submission object
 * @returns {number|null} - The start time in milliseconds or null if not available
 */
export const getStartTimeOfContestForUser = ({ contest, userSubmission }) => {
    if (_isNil(userSubmission)) {
        return null;
    }

    const { startTime: startTimeForUser, submissions } = userSubmission;
    if (!_isNil(startTimeForUser)) {
        return new Date(startTimeForUser).getTime();
    }

    const { startTime: contestStartTime } = contest;
    const firstQuestionSubmissionTime = _head(submissions)?.submissionTime;

    return Math.max(new Date(firstQuestionSubmissionTime).getTime() - 1000, new Date(contestStartTime).getTime());
};

/**
 * Check if a contest is live
 * @param {Contest} contest - The contest object
 * @returns {boolean} - Whether the contest is live
 */
export const isContestLive = (contest) => {
    if (!contest?.startTime || !contest?.endTime) return false;

    const now = getCurrentTimeWithOffset();
    return (
        now >= new Date(contest.startTime).getTime() &&
        now <= new Date(contest.endTime).getTime()
    );
};

/**
 * Check if a contest has ended
 * @param {Contest} contest - The contest object
 * @returns {boolean} - Whether the contest has ended
 */
export const hasContestEnded = (contest) => {
    if (!contest?.endTime) return false;

    const now = getCurrentTimeWithOffset();
    return now > new Date(contest.endTime).getTime();
};

/**
 * Check if a contest is upcoming
 * @param {Contest} contest - The contest object
 * @returns {boolean} - Whether the contest is upcoming
 */
export const isContestUpcoming = (contest) => {
    if (!contest?.startTime) return false;

    const now = getCurrentTimeWithOffset();
    return now < new Date(contest.startTime).getTime();
};

/**
 * Format contest duration to a human-readable string
 * @param {number} durationInSeconds - The duration in seconds
 * @returns {string} - The formatted duration string
 */
export const formatContestDuration = (durationInSeconds) => {
    if (!durationInSeconds) return '0 minutes';

    const minutes = Math.round(durationInSeconds / 60);
    if (minutes < 60) return `${minutes} minute${minutes !== 1 ? 's' : ''}`;

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (remainingMinutes === 0) return `${hours} hour${hours !== 1 ? 's' : ''}`;
    return `${hours} hour${hours !== 1 ? 's' : ''} ${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}`;
};

/**
 * Get the contest status based on its time properties
 * @param {Contest} contest - The contest object
 * @returns {string} - The contest status
 */
export const getContestStatus = (contest) => {
    if (hasContestEnded(contest)) return 'COMPLETED';
    if (isContestLive(contest)) return 'LIVE';
    if (isContestUpcoming(contest)) return 'UPCOMING';
    return 'UNKNOWN';
};