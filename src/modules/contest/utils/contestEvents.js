/**
 * Contest Events Utility Functions
 * This file contains utility functions for tracking contest-related events
 */

import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import { Contest } from '../types/Contest';
import { ANALYTICS_PROPERTIES } from '../constants';
import { getContestStatus } from './contest';

/**
 * Get contest properties to track in analytics events
 * @param {Object} params - The parameters
 * @param {Contest} params.contest - The contest object
 * @returns {Object} - The contest properties to track
 */
export const getContestPropertiesToTrack = ({ contest }) => {
  if (_isEmpty(contest)) {
    return EMPTY_OBJECT;
  }

  const contestId = _get(contest, '_id');
  const contestName = _get(contest, 'name', '');
  const contestStatus = _get(contest, 'status') || getContestStatus(contest);
  const registrationCount = _get(contest, 'registrationCount', 0);
  const hasUserRegistered = !_isEmpty(
    _get(contest, 'currentUserParticipation'),
  );

  return {
    [ANALYTICS_PROPERTIES.CONTEST_ID]: contestId,
    [ANALYTICS_PROPERTIES.CONTEST_NAME]: contestName,
    [ANALYTICS_PROPERTIES.CONTEST_STATUS]: contestStatus,
    [ANALYTICS_PROPERTIES.REGISTRATION_STATUS]: hasUserRegistered
      ? 'REGISTERED'
      : 'NOT_REGISTERED',
    registrationCount,
  };
};

/**
 * Get contest participation properties to track in analytics events
 * @param {Object} params - The parameters
 * @param {Contest} params.contest - The contest object
 * @param {Object} params.participation - The participation object
 * @returns {Object} - The participation properties to track
 */
export const getParticipationPropertiesToTrack = ({
  contest,
  participation,
}) => {
  if (_isEmpty(contest) || _isEmpty(participation)) {
    return EMPTY_OBJECT;
  }

  const contestProperties = getContestPropertiesToTrack({ contest });
  const score = _get(participation, 'score', 0);
  const rank = _get(participation, 'rank');
  const correctSubmission = _get(participation, 'correctSubmission', 0);
  const incorrectSubmission = _get(participation, 'incorrectSubmission', 0);
  const timeSpent = _get(participation, 'timeSpent', 0);

  return {
    ...contestProperties,
    score,
    rank,
    correctSubmission,
    incorrectSubmission,
    timeSpent,
    totalSubmission: correctSubmission + incorrectSubmission,
    accuracy:
      correctSubmission > 0
        ? Math.round(
            (correctSubmission / (correctSubmission + incorrectSubmission)) *
              100,
          )
        : 0,
  };
};

export default {
  getContestPropertiesToTrack,
  getParticipationPropertiesToTrack,
};
