import { StyleSheet } from 'react-native'
import Dark from '@/src/core/constants/themes/dark'

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: Dark.colors.background,
    },
    mainContainerWeb: {
        justifyContent: 'center',
        gap: 24,
        alignItems: 'center',
    },
    container: {
        flex: 1,
        flexDirection: 'column',
        width: '100%',
        height: '100%',
        alignItems: 'center',
        justifyContent: 'space-evenly',
        backgroundColor: Dark.colors.background,
    },
    webContainer: {
        flexDirection: 'column',
        width: '100%',
        height: '100%',
        alignItems: 'center',
        backgroundColor: Dark.colors.background,
        justifyContent: 'center',
        gap: 24,
    },
    mobileHeader: {
        width: '100%',
        alignItems: 'center',
    },
    timerContentContainer: {
        width: '100%',
        minHeight: 300,
    },
    question: {
        flex: 1,
        width: '90%',
        maxHeight: 300,
        maxWidth: 420,
    },
    footerContainer: {
        width: '100%',
        maxWidth: 420,
    },

    // waiting page
    gameTimerContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        gap: 6,
    },
    gameTime: {
        color: 'white',
        fontSize: 20,
        lineHeight: 24,
        fontFamily: 'Montserrat-700'
    },
    timerContainer:{
        gap: 36,
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    startingTimerContentContainer:{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 8,
    },
    startingIn: {
        fontSize: 14,
        color: Dark.colors.textDark,
        fontFamily: 'Montserrat-500'
    },
    leaveChallengeLabel: {
        fontSize: 16,
        color: Dark.colors.secondary,
    }
})

export default styles
