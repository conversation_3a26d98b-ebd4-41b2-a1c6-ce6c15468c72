import _get from 'lodash/get';
import _split from 'lodash/split';
import _toNumber from 'lodash/toNumber';

interface Cell {
  isVisible: boolean;
  value: string;
  type: CellType;
}

type CellType = 'Operand' | 'Operator' | 'EmptyBlock';

interface Puzzle {
  cells: Cell[][];
  availableAnswers: string[];
}

/**
 * Parses a minified puzzle string into a structured Puzzle object
 * @param minifiedPuzzle The minified puzzle string
 * @returns A structured Puzzle object
 */
export function parsePuzzleFromString(minifiedPuzzle: string): Puzzle {
  const parts = minifiedPuzzle.split('|');
  const sizeOfGrid =
    2 * _toNumber(_get(_split(_get(parts, [0]), '_'), [1], 7)) + 1;
  const cells: Cell[][] = Array(sizeOfGrid)
    .fill(null)
    .map(() =>
      Array(sizeOfGrid)
        .fill(null)
        .map(() => ({
          isVisible: true,
          value: '',
          type: 'EmptyBlock' as CellType,
        })),
    );

  let availableAnswers: string[] = [];

  // Parse each part
  for (const part of parts) {
    if (part.startsWith('ANS:')) {
      // Parse available answers
      availableAnswers = part.substring(4).split(',');
    } else if (part.length >= 5) {
      // Parse cell data: rowcol-type-visibility-value
      const row = _toNumber(part[0]);
      const col = _toNumber(part[1]);

      const typeCode = part[2];
      let cellType: CellType = 'Operand';
      if (typeCode === 'P') cellType = 'Operator';
      else if (typeCode === 'E') cellType = 'EmptyBlock';

      const visibilityCode = part[3];
      const isVisible = visibilityCode === 'V';

      const value = part.substring(4);

      // Update cell in grid
      if (row >= 0 && row < sizeOfGrid && col >= 0 && col < sizeOfGrid) {
        cells[row][col] = {
          isVisible,
          value,
          type: cellType,
        };
      }
    }
  }

  return {
    cells,
    availableAnswers,
  };
}

/**
 * Loads a puzzle from a minified string and returns it in the format expected by the UI
 * @param minifiedPuzzle The minified puzzle string
 * @returns A Puzzle object ready for UI consumption
 */
export function getPuzzleFromMinifiedString(questionObject: any): Puzzle {
  const puzzle = {
    ...questionObject,
    question: parsePuzzleFromString(questionObject?.question),
  };
  return puzzle;
}
