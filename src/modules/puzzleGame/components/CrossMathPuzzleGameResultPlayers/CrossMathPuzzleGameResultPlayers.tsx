import React from 'react';
import { View, Text } from 'react-native';
import dark from 'core/constants/themes/dark';
import CrossMathPuzzleGameResultPlayerCard from '../CrossMathPuzzleGameResultPlayerCard';
import useCrossMathPuzzleGameResultPlayersStyles from './CrossMathPuzzleGameResultPlayers.style';

const CrossMathPuzzleGameResultPlayers = ({
  adaptedPlayers,
  player1,
  player2,
  isCurrPlayerWinner,
}: {
  adaptedPlayers: any;
  player1: any;
  player2: any;
  isCurrPlayerWinner: boolean;
}) => {
  const styles = useCrossMathPuzzleGameResultPlayersStyles();

  const renderPlayer = ({ player }: { player: any }) => (
    <View style={styles.playerContainer}>
      <CrossMathPuzzleGameResultPlayerCard user={player} />
    </View>
  );

  return (
    <View style={styles.resultsContainer}>
      {renderPlayer({ player: adaptedPlayers[0] })}
      <View style={styles.scoresContainer}>
        <View style={styles.totalScoreContainer}>
          <View
            style={[
              styles.totalScore,
              isCurrPlayerWinner
                ? { borderColor: dark.colors.victoryColor }
                : { borderColor: dark.colors.tertiary },
            ]}
          >
            <Text style={styles.innerScoreText}>{adaptedPlayers[0].score}</Text>
          </View>
          <Text style={styles.hyphen}>-</Text>
          <View
            style={[
              styles.totalScore,
              !isCurrPlayerWinner
                ? { borderColor: dark.colors.defeatColor }
                : { borderColor: dark.colors.tertiary },
            ]}
          >
            <Text style={styles.innerScoreText}>{adaptedPlayers[1].score}</Text>
          </View>
        </View>
      </View>
      {renderPlayer({ player: adaptedPlayers[1] })}
    </View>
  );
};

export default React.memo(CrossMathPuzzleGameResultPlayers);
