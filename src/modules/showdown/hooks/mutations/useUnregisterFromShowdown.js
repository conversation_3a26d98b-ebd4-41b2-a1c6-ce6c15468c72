import { gql, useMutation } from '@apollo/client'
import { useCallback } from 'react'

const UNREGISTER_FROM_SHOWDOWN = gql`
    mutation UnRegisterForShowdown($showdownId: ID!) {
        unregisterFromShowdown(showdownId: $showdownId)
    }
`

const useUnRegisterFromShowdown = () => {
    const [unregisterFromShowdownMutation] = useMutation(
        UNREGISTER_FROM_SHOWDOWN
    )

    const unregisterFromShowdown = useCallback(
        ({ showdownId }) => {
            return unregisterFromShowdownMutation({ variables: { showdownId } })
        },
        [unregisterFromShowdownMutation]
    )

    return { unregisterFromShowdown }
}

export default useUnRegisterFromShowdown
