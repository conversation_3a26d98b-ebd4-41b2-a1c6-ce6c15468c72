import _isEmpty from 'lodash/isEmpty';
import { useRouter } from 'expo-router';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import Analytics from '../../../core/analytics';
import { ANALYTICS_EVENTS } from '../../../core/analytics/const';
import { PAGE_NAME_KEY, PAGE_NAMES } from '../../../core/constants/pageNames';
import { useSession } from '../../auth/containers/AuthProvider';
import useShowdownRegistration from './useShowdownRegistration';
import showdownReader from '../readers/showdownReader';
import { getShowdownPropertiesToTrack } from '../utils/showdownEvents';
import useCountDownTimer from 'core/hooks/useCountDownTimer';
import SHOWDOWN_ACTIONS from '../constants/showdownActions';
import _get from 'lodash/get';
import { SHOWDOWN_PLAYER_STATUS } from '../constants/showdownPlayerStatus';
import { useApolloClient } from '@apollo/client';

const FIVE_MINUTES_IN_MILLI_SECOND = 5 * 60 * 1000;

const useShowdownDetailsController = ({
  showdown,
  reFetchShowDown,
  onShowdownRegistrationSuccess,
  onShowdownRegistrationFailure,
}) => {
  const currentTime = getCurrentTime();
  const showdownId = showdownReader.id(showdown);
  const { user } = useSession();
  const { isGuest } = user ?? EMPTY_OBJECT;
  const router = useRouter();
  const { isRegisteringForShowDown, onPressRegister, onPressUnRegister } =
    useShowdownRegistration({
      showdown,
      reFetchShowDown,
      onShowdownRegistrationSuccess,
      onShowdownRegistrationFailure,
    });

  const { startTime, endTime,  status} = showdown ?? EMPTY_OBJECT;

  const now = getCurrentTime();

  const [isLive, setIsLive] = useState(
    now >= new Date(startTime).getTime() && now <= new Date(endTime).getTime(),
  );
  const showdownEndTimer = useCountDownTimer({ targetTime: showdown?.endTime });
  const [isAboutToStart, setIsAboutToStart] = useState(false);
  const endTimeStamp = new Date(endTime).getTime();
  const [hasEnded, setHasEnded] = useState(now >= endTimeStamp);
  const refetchRef = useRef(reFetchShowDown);
  refetchRef.current = reFetchShowDown;

  const hasRegistrationEnded = status === 'REGISTRATION_ENDED';

  const { currentRound, isRoundActive } = useMemo(() => {
    const now = Math.floor(getCurrentTime() / 1000);
    const startTimeInSeconds = Math.floor(
      new Date(showdown?.startTime).getTime() / 1000,
    );
    if (now < startTimeInSeconds) {
      return { currentRound: 0, isRoundActive: false };
    }

    const elapsedTime = now - startTimeInSeconds;
    const totalRoundTime = showdown?.roundTime + showdown?.gapBwRounds;
    const roundIndex = Math.floor(elapsedTime / totalRoundTime);

    const timeInCurrentCycle = elapsedTime % totalRoundTime;
    const isRoundActive = timeInCurrentCycle < showdown?.roundTime;

    return { currentRound: roundIndex + 1, isRoundActive };
  }, [showdown, showdownEndTimer]);

  const isBreak = !isRoundActive && isLive;

  const joinEndTime = useMemo(
    () =>
      new Date(showdown?.startTime).getTime() +
      ((currentRound - 1) * (showdown?.roundTime + showdown?.gapBwRounds ?? 0) +
        showdown?.roundConfig?.maxWaitTime -
        5) *
        1000,
    [currentRound, showdown, showdownEndTimer],
  );

  const { roundEndsAt, breakEndsAt } = useMemo(() => {
    const _breakEndsAt =
      new Date(showdown?.startTime).getTime() +
      currentRound *
        ((showdownReader.roundTime(showdown) ?? 0) +
          (showdownReader.gapBwRounds(showdown) ?? 0)) *
        1000;

    if (!isRoundActive) {
      return { breakEndsAt: _breakEndsAt, roundEndsAt: 0 };
    }
    const _roundEndsAt =
      _breakEndsAt - (showdownReader.gapBwRounds(showdown) ?? 0) * 1000;
    return { breakEndsAt: _breakEndsAt, roundEndsAt: _roundEndsAt };
  }, [showdown, currentRound, isRoundActive]);

  //   const currentUserParticipation =
  //     showdownReader.currentUserParticipation(showdown);

  const currentRoundInfo =
    showdownReader.currentRoundInfo(showdown) ?? EMPTY_OBJECT;
  const hasUserRegistered = !_isEmpty(showdown?.currentUserParticipation);

  const userRegisteredAndShowdownIsLive = hasUserRegistered && isLive;
  //   const userIsNotRegisteredAndShowdownIsNotLive =
  //     !hasUserRegistered && !hasEnded;

  const currentGameIdForUser = _get(showdown, [
    'currentUserParticipation',
    'currentGame',
  ]);

  const handleJoinNowGame = useCallback(() => {
    // check the conditions if user is eligible to join
    if (!userRegisteredAndShowdownIsLive || _isEmpty(currentGameIdForUser))
      return;

    Analytics.track(ANALYTICS_EVENTS.CONTEST.CLICKED_ON_JOIN_NOW, {
      ...getShowdownPropertiesToTrack({ showdown }),
      [PAGE_NAME_KEY]: PAGE_NAMES.SHOWDOWN_DETAILS,
    });
    router.push(`showdown/play/${currentGameIdForUser}`);
  }, [userRegisteredAndShowdownIsLive, currentGameIdForUser, showdown, router]);

  const onPressRegisterGuestUser = useCallback(() => {
    showToast({
      type: TOAST_TYPE.ERROR,
      description: 'Guest Users are not allowed to participate in Showdown!',
    });
  }, []);

  const onAction = useCallback(
    ({ type, payload }) => {
      if (_isEmpty(type)) {
        return;
      }
      switch (type) {
        case SHOWDOWN_ACTIONS.REGISTER_FOR_SHOWDOWN: {
          if (isRegisteringForShowDown) {
            return;
          }
          return onPressRegister(payload);
        }
        case SHOWDOWN_ACTIONS.UN_REGISTER_FROM_SHOWDOWN: {
          return onPressUnRegister();
        }
        case SHOWDOWN_ACTIONS.JOIN_NOW: {
          return handleJoinNowGame();
        }
        case SHOWDOWN_ACTIONS.REGISTER_GUEST_USER: {
          return onPressRegisterGuestUser();
        }
        case SHOWDOWN_ACTIONS.REFETCH_SHOWDOWN: {
          return reFetchShowDown?.();
        }
      }
    },
    [
      isRegisteringForShowDown,
      onPressRegister,
      onPressUnRegister,
      handleJoinNowGame,
      onPressRegisterGuestUser,
      reFetchShowDown,
    ],
  );

  const endShowdownTimeoutRef = useRef(null);

  useEffect(() => {
    const currentTime = getCurrentTime();
    const startDateTime = new Date(startTime).getTime();
    const endDateTime =
      startDateTime +
      (showdown?.roundTime + showdown?.gapBwRounds) * showdown?.rounds * 1000;

    if (endShowdownTimeoutRef.current === null && isLive) {
      endShowdownTimeoutRef.current = setTimeout(
        () => setIsLive(false),
        endDateTime - currentTime,
      );
    }

    setIsLive(currentTime >= startDateTime && currentTime <= endDateTime);
    setHasEnded(currentTime >= endDateTime);

    if (startDateTime > currentTime) {
      const timeUntilStart = startDateTime - currentTime;
      setTimeout(() => {
        setIsLive(true);
        refetchRef.current?.();
      }, timeUntilStart);
    }

    if (endDateTime > currentTime) {
      const timeUntilEnd = endDateTime - currentTime;
      setTimeout(() => {
        setIsLive(false);
        setHasEnded(true);
      }, timeUntilEnd);
    }

    const interval = setInterval(() => {
      const currentTime = getCurrentTime();
      if (startDateTime > currentTime) {
        const timeUntilStart = startDateTime - currentTime;
        if (timeUntilStart <= FIVE_MINUTES_IN_MILLI_SECOND) {
          setIsAboutToStart(true);
        }
        if (timeUntilStart <= 0) {
          setIsAboutToStart(false);
          clearInterval(interval);
        }
      } else if (endDateTime < currentTime) {
        setIsLive(false);
        setHasEnded(true);
        clearInterval(interval);
      } else {
        clearInterval(interval);
      }
    }, 1000);

    return () => {
      clearInterval(interval);
      clearTimeout(endShowdownTimeoutRef.current);
    };
  }, [startTime, endTime, showdown]);

  const playerStatus = showdownReader.playerStatus(showdown);
  const hasUserGotBye = playerStatus === SHOWDOWN_PLAYER_STATUS.BYE;
  const hasOpponentNotShown =
    playerStatus === SHOWDOWN_PLAYER_STATUS.OPPONENT_ABSENT;
  const hasFailedToPlay = playerStatus === SHOWDOWN_PLAYER_STATUS.DID_NOT_PLAY;
  const hasUserCompletedTheRound =
    playerStatus === SHOWDOWN_PLAYER_STATUS.ROUND_COMPLETED;
  const canUserJoin =
    currentRoundInfo?.hasJoined ||
    (isRoundActive && currentTime <= joinEndTime && !hasUserCompletedTheRound);

  const state = useMemo(
    () => ({
      showdown,
      hasEnded,
      isLive,
      isBreak,
      joinEndTime,
      currentRound,
      canUserJoin,
      playerStatus,
      hasUserGotBye,
      hasFailedToPlay,
      hasOpponentNotShown,
      hasUserRegistered,
      isRegisteringForShowDown,
      hasUserCompletedTheRound,
      roundEndsAt,
      breakEndsAt,
      currentRoundInfo,
      isRoundActive,
      hasRegistrationEnded
    }),
    [
      showdown,
      isLive,
      playerStatus,
      joinEndTime,
      hasUserCompletedTheRound,
      hasFailedToPlay,
      hasOpponentNotShown,
      isRegisteringForShowDown,
      currentRound,
      hasUserGotBye,
      isBreak,
      hasEnded,
      canUserJoin,
      hasUserRegistered,
      roundEndsAt,
      breakEndsAt,
      currentRoundInfo,
      isRoundActive,
      hasRegistrationEnded
    ],
  );

  return {
    state,
    onAction,
  };
};

export default useShowdownDetailsController;
