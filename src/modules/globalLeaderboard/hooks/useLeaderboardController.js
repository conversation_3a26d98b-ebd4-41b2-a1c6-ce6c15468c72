import { useCallback, useMemo, useRef, useState } from 'react';

import _size from 'lodash/size';
import _isNil from 'lodash/isNil';
import userReader from '@/src/core/readers/userReader';
import _get from 'lodash/get';
import useLeaderboardQuery from './useLeaderboardQuery';
import { useSession } from '../../auth/containers/AuthProvider';
import Analytics from '../../../core/analytics';
import { ANALYTICS_EVENTS } from '../../../core/analytics/const';
import useFriendsLeaderboardQuery from './useFriendsLeaderboardQuery';
import {
  getRatingTypeFromTitle,
  LEADERBOARD_TYPES,
  RATING_KEYS,
} from '../constants/leaderboardConstants';

const COUNTRY_FILTER_KEYS = {
  WORLD: 'WORLD',
  COUNTRY: 'COUNTRY',
};

const RATING_VS_RANK = {};
const PAGE_SIZE = 50;

const useLeaderboardController = () => {
  const { user } = useSession();
  const hasReportedSearch = useRef(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [topThreePlayers, setTopThreePlayers] = useState([]);
  const [paginatedData, setPaginatedData] = useState([]);
  const [podiumLoading, setPodiumLoading] = useState(true);
  const [selectedCountryFilter, setSelectedCountryFilter] = useState(
    COUNTRY_FILTER_KEYS.WORLD,
  );

  const countryCode =
    selectedCountryFilter === COUNTRY_FILTER_KEYS.COUNTRY
      ? user?.countryCode
      : null;
  const [pageNumber, setPageNumber] = useState(1);
  const { loading, error, fetchLeaderboard } = useLeaderboardQuery({
    searchKey: _size(searchQuery) < 3 ? '' : searchQuery,
    countryCode,
    page: pageNumber,
  });

  const {
    loading: loadingFriends,
    error: errorFriends,
    fetchFriendsLeaderboard,
  } = useFriendsLeaderboardQuery({
    page: pageNumber,
  });

  // const users = useMemo(() => _map(edges, 'node'), [edges]);

  const onSearchQueryChange = useCallback((searchText) => {
    if (!hasReportedSearch.current && _size(searchText) > 0) {
      hasReportedSearch.current = true;
      Analytics.track(ANALYTICS_EVENTS.SEARCH_PLAYER_ON_LEADERBOARD, {
        playerName: searchText,
      });
    }
    setSearchQuery(searchText);
  }, []);

  const adaptLeaderboardRanking = useCallback(
    ({ edges, pageNumber, ratingType }) =>
      edges.map((edge, index) => {
        if (_isNil(edge)) return edge;
        if (_isNil(edge?.node)) return edge;

        const { node } = edge;
        const { rating, globalRank } = node;
        let updatedRank = globalRank;

        let ratingToUse;
        switch (ratingType) {
          case RATING_KEYS.ABILITY:
            ratingToUse = userReader.abilityDuelsRating(node);
            break;
          case RATING_KEYS.MEMORY:
            ratingToUse = userReader.flashAnzanRating(node);
            break;
          default:
            ratingToUse = rating;
            break;
        }

        if (_isNil(_get(RATING_VS_RANK, [ratingType, ratingToUse]))) {
          updatedRank = (pageNumber - 1) * 50 + (index + 1);
          RATING_VS_RANK[ratingType] = {
            ..._get(RATING_VS_RANK, [ratingType]),
            ratingToUse: updatedRank,
          };
        } else {
          updatedRank = _get(RATING_VS_RANK, [ratingType, ratingToUse]);
        }
        return {
          ...edge,
          node: {
            ...node,
            globalRank: updatedRank,
          },
        };
      }),
    [],
  );

  const fetchLeaderboardEntities = useCallback(
    async ({ pageNumber, ratingType }) => {
      setPageNumber(pageNumber);
      const result = await fetchLeaderboard({ pageNumber, ratingType });
      if (result?.data?.leaderboardNew) {
        const data = result.data.leaderboardNew;

        return {
          data: adaptLeaderboardRanking({
            edges: data?.edges,
            pageNumber,
            ratingType,
          }),
          totalItems: data?.totalCount,
        };
      }
      return EMPTY_OBJECT;
    },
    [fetchLeaderboard, adaptLeaderboardRanking],
  );

  const fetchFriendsLeaderboardEntities = useCallback(
    async ({ pageNumber, ratingType }) => {
      setPageNumber(pageNumber);
      const result = await fetchFriendsLeaderboard({ pageNumber, ratingType });
      if (result?.data?.getFriendsLeaderboard) {
        const data = result.data.getFriendsLeaderboard;
        return {
          data: adaptLeaderboardRanking({
            edges: data?.edges,
            pageNumber,
            ratingType,
          }),
          totalItems: data?.totalCount,
        };
      }
      return EMPTY_OBJECT;
    },
    [fetchFriendsLeaderboard, adaptLeaderboardRanking],
  );

  const fetchPodiumPlayers = useCallback(
    async ({ type, title }) => {
      try {
        setPodiumLoading(true);
        const fetchFunction =
          type === LEADERBOARD_TYPES.GLOBAL
            ? fetchLeaderboardEntities
            : fetchFriendsLeaderboardEntities;

        const result = await fetchFunction({
          pageNumber: 1,
          ratingType: getRatingTypeFromTitle(title),
        });

        if (result?.data) {
          setTopThreePlayers(result.data.slice(0, 3));
          setPaginatedData(result.data.slice(3));
        }
        return result?.data;
      } catch (err) {
        console.error('Error fetching podium players:', err);
        return null;
      } finally {
        setPodiumLoading(false);
      }
    },
    [fetchLeaderboardEntities, fetchFriendsLeaderboardEntities],
  );

  const fetchPaginatedData = useCallback(
    async ({ type, title, pageNumber }) => {
      const fetchFunction =
        type === LEADERBOARD_TYPES.GLOBAL
          ? fetchLeaderboardEntities
          : fetchFriendsLeaderboardEntities;

      const result = await fetchFunction({
        pageNumber,
        ratingType: getRatingTypeFromTitle(title),
      });

      if (pageNumber === 1) {
        return {
          ...result,
          data: result.data.slice(3),
        };
      }
      return result;
    },
    [fetchLeaderboardEntities, fetchFriendsLeaderboardEntities],
  );

  const countryFilterTabs = useMemo(
    () => [
      { id: COUNTRY_FILTER_KEYS.WORLD, label: 'World Ranking' },
      {
        id: COUNTRY_FILTER_KEYS.COUNTRY,
        label: 'Country Ranking',
      },
    ],
    [],
  );

  const onCountryFilterTabPress = useCallback(
    ({ tab }) => {
      setSelectedCountryFilter(tab.id);
    },
    [setSelectedCountryFilter],
  );

  const fetchDataOfPage = useCallback(
    ({ pageNumber = 1 }) => {
      setPageNumber(pageNumber);
    },
    [setPageNumber, pageNumber],
  );

  const countryFilterProps = useMemo(
    () => ({
      tabs: countryFilterTabs,
      selectedTabId: selectedCountryFilter,
      onSelectTab: onCountryFilterTabPress,
    }),
    [countryFilterTabs, selectedCountryFilter, onCountryFilterTabPress],
  );

  return {
    // users,
    loading,
    error,
    searchQuery,
    selectedCountryFilter,
    pageNumber,
    topThreePlayers,
    paginatedData,
    podiumLoading,
    fetchLeaderboardEntities,
    fetchFriendsLeaderboardEntities,
    fetchPodiumPlayers,
    fetchPaginatedData,
    onSearchQueryChange,
    countryCode,
    countryFilterProps,
  };
};

export default useLeaderboardController;
