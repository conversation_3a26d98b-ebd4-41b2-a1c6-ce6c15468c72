import _map from 'lodash/map';
import _isEqual from 'lodash/isEqual';
import React, { useCallback, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import Header from 'shared/Header';
import StatikCoinLeaderboardRow from '../../components/StatikCoinLeaderboardRow';
import STATIK_COIN_LEADERBOARD_TYPE from '../../constants/statikCoinLeaderboardTypes';
import useGetStatikCoinsLeaderboard from '../../hooks/queries/useGetStatikCoinsLeaderboard';
import useStatikCoinLeaderboardStyles from './StatikCoinLeaderboard.style';
import dark from 'core/constants/themes/dark';
import PaginatedList from 'shared/PaginatedList';
import StatikCoinLeaderboardHeader from '../../components/StatikCoinLeaderboardHeader';

const PAGE_SIZE = 50;

const StatikCoinLeaderboard = (props) => {
  const styles = useStatikCoinLeaderboardStyles();
  const [selectedDuration, setSelectedDuration] = useState(
    STATIK_COIN_LEADERBOARD_TYPE[0].duration,
  );

  const { fetchStatikCoinsLeaderboard } = useGetStatikCoinsLeaderboard({
    pageSize: PAGE_SIZE,
  });

  const renderLeaderboardRowItem = useCallback(({ item, index }) => {
    return (
      <View
        key={`${index}`}
        style={{
          marginVertical: 5,
          paddingVertical: 10,
          borderBottomColor: dark.colors.tertiary,
          borderBottomWidth: 1,
        }}
      >
        <StatikCoinLeaderboardRow {...item} />
      </View>
    );
  }, []);

  const fetchStatikCoinsLeaderboardData = useCallback(
    async ({ pageNumber }) => {
      const response = await fetchStatikCoinsLeaderboard({
        pageNumber,
        leaderboardType: selectedDuration,
      });
      const { data } = response;
      const { statikCoinsLeaderboard: statikCoinsLeaderboardObj } =
        data ?? EMPTY_OBJECT;
      const { results, totalResults } = statikCoinsLeaderboardObj;
      return { data: results, totalItems: totalResults };
    },
    [fetchStatikCoinsLeaderboard, selectedDuration],
  );

  return (
    <View style={styles.container}>
      <Header title={'Statik Leaderboard'} />
      <View style={styles.innerContainer}>
        <View style={styles.filterRow}>
          {_map(STATIK_COIN_LEADERBOARD_TYPE, (item, index) => {
            const isSelected = _isEqual(item.duration, selectedDuration);

            return (
              <TouchableOpacity
                key={`${item.key}-${index}`}
                style={[
                  styles.unSelectedFilterContainer,
                  isSelected && { borderColor: dark.colors.secondary },
                ]}
                onPress={() => setSelectedDuration(item.duration)}
              >
                <Text
                  style={[
                    styles.unSelectedFilterText,
                    isSelected && { color: dark.colors.secondary },
                  ]}
                >
                  {item.title}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>

        <View style={styles.listComponent}>
          <PaginatedList
            key={selectedDuration}
            placeholderComponent={null}
            fetchData={fetchStatikCoinsLeaderboardData}
            renderItem={renderLeaderboardRowItem}
            renderHeader={() => <StatikCoinLeaderboardHeader />}
            pageSize={PAGE_SIZE}
            keyExtractor={(item, index) => ` ${index}`}
            contentContainerStyle={EMPTY_OBJECT}
            listFooterComponent={<View style={{ height: 80 }}></View>}
            dataKey={'statikCoinsLeaderboard'}
          />
        </View>
      </View>
    </View>
  );
};

StatikCoinLeaderboard.propTypes = {};

export default React.memo(StatikCoinLeaderboard);
