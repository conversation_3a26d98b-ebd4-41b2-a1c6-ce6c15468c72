import React from 'react';
import { Image, View } from 'react-native';
import { Text } from '@rneui/themed';

import rec1 from '@/assets/images/Rectangle5.png';
import rec2 from '@/assets/images/Rectangle6.png';

import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import UserImage from 'atoms/UserImage/UserImage';

import _isEmpty from 'lodash/isEmpty';
import styles from './PlayerResultCard.style';
import dark from '../../../../core/constants/themes/dark';
import userReader from '../../../../core/readers/userReader';

const PlayerResultCard = (props) => {
  const { user } = props;
  const { user: currentUser } = useSession();
  const isCurrentUser = user?._id === currentUser?._id;

  const { score, isWinner } = user;

  if (_isEmpty(user)) {
    // TODO: render loader state,
    return null; // Placeholder, you can replace this with a loader component
  }

  return (
    <View
      style={[
        styles.background,
        isWinner ? styles.winnerContainer : styles.loserContainer,
      ]}
    >
      <View
        style={{
          backgroundColor: dark.colors.primary,
          borderRadius: 7,
          minHeight: 108,
        }}
      >
        <Image
          source={rec1}
          style={[styles.image1, !isCurrentUser && { opacity: 0 }]}
        />
        <Image
          source={rec2}
          style={[styles.image2, !isCurrentUser && { opacity: 0 }]}
        />
        <View style={styles.card}>
          <UserImage size={34} user={user} />
          <View style={styles.userInfo}>
            <Text style={styles.userName} numberOfLines={1}>
              {userReader.username(user)}
            </Text>
            <Text style={styles.userScore}>{score}</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default React.memo(PlayerResultCard);
