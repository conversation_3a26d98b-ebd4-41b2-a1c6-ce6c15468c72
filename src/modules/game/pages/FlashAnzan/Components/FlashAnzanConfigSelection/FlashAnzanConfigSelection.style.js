import { StyleSheet } from "react-native";
import dark from "../../../../../../core/constants/themes/dark";

const styles = StyleSheet.create({
    formContainer: {
        borderRadius: 12,
        borderWidth: 1,
        borderColor: dark.colors.tertiary,
        paddingHorizontal: 17,
        paddingVertical: 8
    },
    mainContainer: {
        gap: 20,
        marginBottom: 70
    },
    infoContainer: {
        backgroundColor: dark.colors.primary,
        borderRadius: 8,
        paddingVertical: 12,
        paddingHorizontal: 24,
        gap: 16
    },
    infoItemRow: {
        flexDirection: "row",
        justifyContent: "space-between"
    },
    infoText: {
        fontFamily: "Montserrat-500",
        fontSize: 12,
        lineHeight: 15,
        color: "white",
        textAlign: "left"
    },
    valueText: {
        fontFamily: "Montserrat-700",
        fontSize: 16,
        lineHeight: 20,
        color: "white",
        textAlign: "right"
    }
})

export default styles