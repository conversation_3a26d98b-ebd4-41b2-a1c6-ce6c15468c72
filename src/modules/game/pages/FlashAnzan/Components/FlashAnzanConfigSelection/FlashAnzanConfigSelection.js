import React, { useMemo } from "react"
import { View, Text } from "react-native"
import NumberInput from "../../../../../practice/components/NumberInput/NumberInput"
import CheckBoxInput from "../../../../../practice/components/CheckboxInput/CheckBoxInput"
import styles from "./FlashAnzanConfigSelection.style"
import PropTypes from "prop-types"
import getMaxScoreOfFlashAnzanQueFromConfig from "../../../../utils/getMaxScoreOfFlashAnzanQueByConfig"

const FlashAnzanConfigSelection = (props) => {
    const { selectedConfig, updateConfig } = props

    const { digits, flashSpeed, includeSubstraction, numberCount } = selectedConfig ?? EMPTY_OBJECT

    const numberOfQuestions = useMemo(() => {
        return numberCount
    }, [numberCount])

    const maxSore = useMemo(() => {
        return getMaxScoreOfFlashAnzanQueFromConfig({ config: selectedConfig })
    }, [selectedConfig])

    return (
        <View style={styles.mainContainer}>
            <View style={styles.formContainer}>
                <NumberInput
                    label="Number of digits"
                    value={digits}
                    onValueChange={(value) => updateConfig('digits', value)}
                    minValue={1}
                    maxValue={10}
                />
                <NumberInput
                    label="Flash Speed(in ms)"
                    value={flashSpeed}
                    minValue={200}
                    onValueChange={(value) => updateConfig('flashSpeed', value)}
                    maxValue={5000}
                    incrementBy={100}
                />
                <CheckBoxInput
                    label="Include subtraction"
                    value={includeSubstraction}
                    onValueChange={(value) => updateConfig('includeSubstraction', value)}
                />
            </View>
            <View style={styles.infoContainer}>
                <View style={styles.infoItemRow}>
                    <Text style={styles.infoText}>
                        # numbers
                    </Text>
                    <Text style={styles.valueText}>
                        {numberOfQuestions}
                    </Text>
                </View>
                <View style={styles.infoItemRow}>
                    <Text style={styles.infoText}>
                        Max score possible
                    </Text>
                    <Text style={styles.valueText}>
                        {maxSore}
                    </Text>
                </View>
            </View>
        </View>
    )
}

FlashAnzanConfigSelection.propTypes = {
    selectedConfig: PropTypes.object,
    updateConfig: PropTypes.func
}

export default React.memo(FlashAnzanConfigSelection)