import {StyleSheet} from "react-native";
import useMediaQuery from "core/hooks/useMediaQuery";
import {useMemo} from "react";
import dark from "core/constants/themes/dark";

const createStyles = (isCompactMode) => StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center"
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
    flex: 1.8,
  },
  userDetailsTextContainer: {},
  statikCoinsDetail: {
    justifyContent: "flex-end",
    alignItems: "center",
    flex: 0.3,
    flexDirection: "row",
    gap: 3
  },
  userNameText: {
    fontFamily: "Montserrat-400",
    fontSize: 14,
    lineHeight: 20,
    maxWidth: 140,
    color: "white"
  },
  ratingText: {
    fontFamily: "Montserrat-600",
    fontSize: 11,
    lineHeight: 13,
    color: dark.colors.textDark
  },
  rankText: {
    flex: 0.3,
    fontFamily: "Montserrat-700",
    fontSize: 14,
    color: "white"
  },
  userImage: {
    overflow: "hidden",
    height: 32,
    width: 32,
    borderRadius: 4
  },
  statikCoinsText: {
    fontFamily: "Montserrat-600",
    fontSize: 14,
    color: "white",
    textAlign: 'left',
    lineHeight: 20
  },
  currentQuesScoreText: {
    fontFamily: "Montserrat-500",
    fontSize: 9,
    color: "white",
    textAlign: 'left',
    lineHeight: 12,
    minWidth: 10,
  },
  emptyCurrentQuesScoreText: {
    minWidth: 10,
  },
  statikCoinsIcon: {
    height: 18,
    width: 18
  }
})

const useGroupPlayLeaderboardRowStyles = () => {
  const {isMobile} = useMediaQuery()

  const styles = useMemo(() => createStyles(isMobile), [isMobile])

  return styles
}

export default useGroupPlayLeaderboardRowStyles