import dark from 'core/constants/themes/dark';
import { Dimensions, StyleSheet } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useMemo } from 'react';

const createStyles = (isCompactMode) =>
  StyleSheet.create({
    container: {
      flex: 1,
      width: '100%',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'transparent',
      paddingTop: 0,
      marginTop: 0,
    },
    innerContainer: {
      flex: 1,
      paddingHorizontal: isCompactMode ? 25 : 20,
      width: isCompactMode ? '100%' : Dimensions.get('window').width * 0.35,
      // height: "100%",
      gap: 24,
      borderRadius: isCompactMode ? 0 : 12,
      borderWidth: isCompactMode ? 0 : 1,
      borderColor: dark.colors.tertiary,
      paddingVertical: isCompactMode ? 0 : 20,
      // paddingBottom: 30,
      marginVertical: isCompactMode ? 16 : 20,
    },
    gradientBg: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      width: '100%',
    },
    overlayStyle: {
      maxWidth: Math.min(Dimensions.get('window').width - 16, 400),
      padding: 0,
      borderRadius: 13,
    },
    trailingComponent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
      gap: 4,
      borderWidth: 0.5,
      borderColor: dark.colors.tertiary,
      borderRadius: 12,
      padding: 12,
    },
    newGameText: {
      fontSize: 12,
      fontFamily: 'Montserrat-600',
      color: dark.colors.secondary,
    },
  });

const useGameResultStyles = () => {
  const { isMobile } = useMediaQuery();

  const styles = useMemo(() => createStyles(isMobile), [isMobile]);

  return styles;
};

export default useGameResultStyles;
