import dark from "core/constants/themes/dark";
import { Dimensions, StyleSheet } from "react-native";
import useMediaQuery from "core/hooks/useMediaQuery";
import { useMemo } from "react";

const createStyles = (isCompactMode) => StyleSheet.create({
    container: {
        flex: 1,
        width: "100%",
        alignItems: "center"
    },
    headerContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingBottom: 18,
    },
    headerText: {
        color: dark.colors.textDark,
        lineHeight: 12,
        fontSize: 10,
        fontFamily: 'Montserrat-500',
    },
    scrollViewContainer: {
        flex: 1,
        alignSelf: 'center',
        width: isCompactMode ? "100%" : Dimensions.get("window").width * 0.6,
    },
    innerContainer: {
        flex:1,
        paddingHorizontal: isCompactMode ? 16 : 20,
        width: isCompactMode ? "100%" : Dimensions.get("window").width * 0.6,
        // height: "100%",
        gap: 24,
        borderRadius: isCompactMode ? 0 : 12,
        borderWidth: isCompactMode ? 0 : 1,
        borderColor: dark.colors.tertiary,
        paddingVertical: isCompactMode ? 0 : 20,
        // paddingBottom: 30,
        marginVertical: isCompactMode ? 0 : 20
    },
    gradientBox: {
        padding: 1,
        borderWidth: 1,
        borderColor: dark.colors.tertiary,
        borderRadius: 8,
    },
    userBox: {
        gap: 8,
        flexDirection: 'row',
        width: 131,
        alignItems: 'center',
        overflow: 'hidden',
        borderRadius: 8,
        backgroundColor: dark.colors.primary,
        padding: 10,
    },
    userName: {
        color: dark.colors.textDark,
        lineHeight: 15,
        width: 70,
        fontSize: 12,
        fontFamily: 'Montserrat-500',
    },
    score: {
        color: 'white',
        fontSize: 16,
    },
    winnerScore: {
        color: dark.colors.secondary,
        fontSize: 16,
    },
    headerRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 12,
        paddingHorizontal: 12,
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#333',
    },
    timeColumn: {
        fontFamily: 'Montserrat-500',
        flex: 1,
        color: '#fff',
        textAlign: 'center',
        paddingHorizontal: 8,
    },
    questionColumn: {
        fontFamily: 'Montserrat-500',
        flex: 3,
        color: '#fff',
        textAlign: 'center',
        paddingHorizontal: 8,
    },
    fastTime: {
        fontFamily: 'Montserrat-500',
        color: dark.colors.secondary,
    },
    slowTime: {
        fontFamily: 'Montserrat-500',
        color: dark.colors.textDark,
    },
    fab: {
        position: 'absolute',
        bottom: 20,
        marginHorizontal: 16,
        width: '90%',
        left: 0,
        backgroundColor: dark.colors.secondary,
        paddingVertical: 10,
        borderRadius: 50,
    },
    fabText: {
        textAlign: 'center',
        color: dark.colors.background,
        fontSize: 14,
        fontFamily: 'Montserrat-600',
        lineHeight: 20
    },
    overlayStyle: {
        maxWidth: Math.min(Dimensions.get("window").width - 16, 400),
        padding: 0,
        borderRadius: 13,
    }
});

const useGameAnalysisStyles = () => {
    const { isMobile } = useMediaQuery()

    const styles = useMemo(() => createStyles(isMobile), [isMobile])

    return styles
}

export default useGameAnalysisStyles