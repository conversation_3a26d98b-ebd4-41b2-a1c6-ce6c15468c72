import Dark from "core/constants/themes/dark"
import { StyleSheet } from "react-native"

const styles = StyleSheet.create({
    container: {
        width: '100%',
        maxWidth: 420,
        flexDirection: 'row',
    },
    inputStyle: {
        width: '100%',
        fontSize: 20,
        color: 'white',
        fontFamily: 'Montserrat-400',
        paddingHorizontal: 8,
        paddingVertical: 10,
    },
    inputStyleDesktop: {
        width: '100%',
        fontSize: 20,
        color: 'white',
        fontFamily: 'Montserrat-400',
        outlineStyle: 'none',
        borderBottomWidth: 2,
        borderBottomColor: Dark.colors.stroke,
        paddingHorizontal: 8,
        paddingVertical: 10,
    },

    errorText: {
        color: Dark.colors.errorDark,
        fontSize: 24,
        marginLeft: 10,
    },
    errorContainer: {
        position: 'absolute',
        alignSelf: 'center',
        right: 16,
        top: 12,
        alignItems: 'center',
        justifyContent: 'center',
    },
    closeIconContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center',
    },
})

export default styles