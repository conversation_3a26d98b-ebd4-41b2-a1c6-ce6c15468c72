import { gql, useQuery } from '@apollo/client';
import _castArray from 'lodash/castArray';
import _compact from 'lodash/compact';
import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';
import _get from 'lodash/get';

export const GET_PUZZLE_SUBMISSIONS_BY_MONTH_QUERY = gql(`
    query GetPuzzleSubmissionsByMonthByType($yearMonths: [String!]!, $puzzleType: PuzzleType!) {
        getPuzzleSubmissionsByMonthByType(yearMonths: $yearMonths, puzzleType: $puzzleType) {
            yearMonth
            puzzleSubmissions {
                id
                userId
                puzzleId
                timeSpent
                completedAt
                statikCoinsEarned
                puzzleDate
                puzzleType
            }
        }
    }
`);

const useGetPuzzleSubmissionsByMonth = ({
  yearMonths,
  puzzleType = PUZZLE_TYPES.CROSS_MATH_PUZZLE,
}: {
  yearMonths: string[];
  puzzleType?: string;
}) => {
  const { data, loading, error } = useQuery(
    GET_PUZZLE_SUBMISSIONS_BY_MONTH_QUERY,
    {
      variables: {
        yearMonths: _compact(_castArray(yearMonths)),
        puzzleType,
      },
      fetchPolicy: 'cache-first',
      notifyOnNetworkStatusChange: true,
    },
  );

  return {
    loading,
    error,
    data: _get(data, 'getPuzzleSubmissionsByMonthByType', EMPTY_OBJECT),
  };
};

export default useGetPuzzleSubmissionsByMonth;
