import React from 'react';
import { Pressable, Text } from 'react-native';
import Rive from 'atoms/Rive';
import RIVE_ANIMATIONS from 'core/constants/riveAnimations';
import { PUZZLE_TYPES } from 'modules/puzzles/types/puzzleType';
import styles from './PuzzleDCTypeCard.style';

const PuzzleDCTypeCard = ({
  onPress,
  type,
}: {
  onPress: () => void;
  type: string;
}) => {
  const puzzleTypeText =
    type === PUZZLE_TYPES.CROSS_MATH_PUZZLE ? 'Cross-Math' : 'Ken-Ken';
  return (
    <Pressable style={styles.container} onPress={onPress}>
      <Rive
        url={RIVE_ANIMATIONS.PUZZLE_ANIMATION}
        autoPlay
        style={{ width: 150, height: 150 }}
      />
      <Text style={styles.puzzleTypeText}>{puzzleTypeText}</Text>
    </Pressable>
  );
};

export default React.memo(PuzzleDCTypeCard);
