import React from 'react';
import LinearGradient from 'atoms/LinearGradient';
import { Text, TouchableOpacity, View } from 'react-native';
import { format } from 'date-fns';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import ShimmerView from 'molecules/ShimmerView';
import dark from 'core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';
import _compact from 'lodash/compact';
import Haptics from 'core/container/Haptics';
import styles, { DATE_DIMENSION } from './CalendarDate.style';

const CalendarDate = ({
  item,
  onClick,
  isCompleted = false,
  isSelected = false,
  loading = false,
}) => {
  const currentDate = new Date(getCurrentTime());
  const today = format(currentDate, 'yyyy-MM-dd');

  const isToday = item?.dateString === today;

  const isFutureDate = item?.dateString > today;

  const renderLoadingDate = () => <ShimmerView style={styles.dateBorder} />;

  const renderDateContent = () => (
    <View
      style={_compact([
        styles.dateBorder,
        isCompleted && styles.crownContainer,
        isSelected && styles.selectedBackground,
        isToday && styles.todayBackground,
        { borderRadius: 9 },
      ])}
    >
      {isCompleted ? (
        <LinearGradient
          colors={[withOpacity(dark.colors.puzzle.primary, 0.3), 'transparent']}
          start={{
            x: 1,
            y: 1,
          }}
          end={{
            x: 0,
            y: 0,
          }}
          style={{
            height: DATE_DIMENSION - 2,
            width: DATE_DIMENSION - 2,
            borderRadius: 9,
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <View style={styles.completedIconContainer}>
            <FontAwesome
              name="check"
              size={7}
              color={isToday ? 'black' : 'white'}
            />
          </View>
          <Text style={[styles.dateText, isToday && styles.todayText]}>
            {item?.day}
          </Text>
        </LinearGradient>
      ) : (
        <Text
          style={[
            styles.dateText,
            isToday && styles.todayText,
            isFutureDate && styles.futureText,
          ]}
        >
          {item?.day}
        </Text>
      )}
    </View>
  );

  const onClickDate = () => {
    if (isFutureDate) {
      return;
    }
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onClick?.({ date: item?.dateString });
  };

  if (item?.isEmpty) {
    return <View key={item?.dateString} style={styles.emptyDateBox} />;
  }

  return (
    <TouchableOpacity
      key={item.dateString}
      style={styles.dateCell}
      onPress={onClickDate}
      disabled={loading}
    >
      {loading ? renderLoadingDate() : renderDateContent()}
    </TouchableOpacity>
  );
};

export default React.memo(CalendarDate);
