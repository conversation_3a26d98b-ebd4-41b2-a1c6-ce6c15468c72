import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import { useMemo } from 'react';
import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const createStyles = (isCompactMode: boolean) =>
  StyleSheet.create({
    container: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
    solvePuzzleButton: {
      backgroundColor: dark.colors.secondary,
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderRadius: 20,
    },
    innerContainer: {
      flex: 1,
      alignItems: 'center',
    },
    buttonText: {
      fontSize: isCompactMode ? 12 : 13,
      color: '#000',
      fontFamily: 'Montserrat-600',
    },
    crossMathPuzzleLabelsContainer: {
      alignItems: 'center',
      gap: 8,
      padding: 16,
    },
    crossMathPuzzleTitle: {
      fontSize: isCompactMode ? 28 : 36,
      color: dark.colors.puzzle.primary,
      letterSpacing: 2,
      fontFamily: 'Montserrat-700',
      textAlign: 'center',
      lineHeight: isCompactMode ? 33 : 37,
    },
    crossMathPuzzleDate: {
      fontSize: isCompactMode ? 16 : 20,
      color: dark.colors.textDark,
      fontFamily: 'Montserrat-700',
    },
    actionsContainer: {
      flexDirection: 'row',
      gap: 12,
      padding: 16,
      paddingBottom: 20,
      alignItems: 'center',
      justifyContent: 'center',
      borderTopWidth: isCompactMode ? 2 : 0,
      borderColor: dark.colors.tertiary,
      width: '100%',
      maxWidth: 400,
      borderRadius: 12,
    },
    tabBarMainContainer: {
      flex: 1,
      width: '100%',
      // maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
    },
    tabHeaderContainer: {
      flexDirection: 'row',
      // marginHorizontal: 16,
      paddingHorizontal: 16,
      justifyContent: 'space-around',
      alignItems: 'center',
      gap: 16,
      height: 80,
      minHeight: 90,
    },
    buttonStyle: {
      borderWidth: 0.8,
      borderColor: dark.colors.tertiary,
      borderRadius: 8,
      height: 36,
    },
    buttonContainerStyle: {
      width: 40,
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 12,
      height: 50,
    },
    buttonTextStyle: {
      fontSize: 12,
      fontFamily: 'Montserrat-700',
      color: dark.colors.textLight,
    },
    buttonBorderBackgroundStyle: {
      width: 40,
      backgroundColor: dark.colors.tertiary,
      height: 20,
      bottom: 2,
    },
  });

const usePuzzleHomePageStyles = () => {
  const { isMobile: isCompactMode } = useMediaQuery();

  const styles = useMemo(() => createStyles(isCompactMode), [isCompactMode]);

  return styles;
};

export default usePuzzleHomePageStyles;
