import React from 'react';
import { cellType, footerType } from 'modules/puzzles/types/crossMathCellType';

type CrossMathPuzzlePracticeContextValue = {
  state: {
    puzzle: any;
    gridItems: cellType[];
    startTime: number;
    dimension: { rows: number; columns: number };
    footerItems: footerType[];
    selectedFooterCell: footerType | null;
    hasSolved: boolean;
    isPuzzleFilled: boolean;
    isIncorrectSolution: boolean;
    incorrectAttemptCount: number;
    selectedGridCell: number | null;
    loading: boolean;
    error: any;
    canRedo: boolean;
    canUndo: boolean;
    prevTimeSpent: number;
  };
  onAction: (payload: any) => void;
};

const CrossMathPuzzlePracticeContext =
  React.createContext<CrossMathPuzzlePracticeContextValue>(
    {} as CrossMathPuzzlePracticeContextValue,
  );

export default CrossMathPuzzlePracticeContext;

export const useCrossMathPuzzlePracticeContext = () =>
  React.useContext(CrossMathPuzzlePracticeContext);
