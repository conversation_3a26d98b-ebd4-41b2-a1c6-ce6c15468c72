import React, { useCallback, useState } from 'react';
import { ScrollView, View } from 'react-native';
import Header from 'shared/Header';
import Feather from '@expo/vector-icons/Feather';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import PropTypes from 'prop-types';
import UserCommitmentTracker from 'modules/home/<USER>/UserCommitmentTimeTracker';
import styles from '../Profile.style';
import ProfileRecentGames from '../../../components/Last10PlayedGames';
import EditProfilePopover from '../../../components/EditProfilePopover';
import LogoutOverlay from '../../../../home/<USER>/MobileDrawerScreen/LogoutOverlay';
import UserProfileCard from '../../../components/UserProfileCard';
import RatingChangeStats from '../../../components/RatingChangeStats';
import UserStatistics from '../../../components/UserStatistics';
import UserAchievement from '../../../components/UserAchievements';
import InsightsSection from '../../Insights/components/InsightsSection';
import SocialLinks from '../../../components/SocialLinks';

const CompactProfilePage = (props) => {
  const {
    user,
    recentGames,
    statsData,
    signOut,
    isCurrentUser,
    userAdditionalInfo,
  } = props;

  const [isPopoverVisible, setPopoverVisible] = useState(false);

  const [isOverlayVisible, setIsOverlayVisible] = useState(false);

  const toggleOverlay = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.PROFILE.CLICKED_ON_PROFILE_PAGE_DELETE_ACCOUNT,
    );
    setPopoverVisible(false);
    setIsOverlayVisible((prev) => !prev);
  }, [setIsOverlayVisible]);

  const togglePopover = useCallback(() => {
    Analytics.track(
      ANALYTICS_EVENTS.PROFILE.CLICKED_ON_PROFILE_PAGE_KEBAB_MENU,
    );
    setPopoverVisible((prevPopoverVisible) => !prevPopoverVisible);
  }, []);

  const confirmLogout = useCallback(() => {
    toggleOverlay();
    signOut?.();
  }, [toggleOverlay, signOut]);

  const renderTrailingComponent = useCallback(
    () => (
      <Feather
        name="more-vertical"
        size={20}
        color="white"
        onPress={togglePopover}
      />
    ),
    [togglePopover],
  );

  return (
    <View style={styles.compactProfileMainContainer}>
      <Header
        title="Profile"
        onRightButtonPress={togglePopover}
        renderTrailingComponent={
          !isCurrentUser ? null : renderTrailingComponent
        }
      />

      {isCurrentUser && (
        <EditProfilePopover
          isVisible={isPopoverVisible}
          onBackdropPress={togglePopover}
          onDeletePressed={toggleOverlay}
        />
      )}

      <ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={styles.container}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.userCard}>
          <UserProfileCard
            user={user}
            isCurrentUser={isCurrentUser}
            userAdditionalInfo={userAdditionalInfo}
          />
        </View>

        {isCurrentUser && <UserCommitmentTracker />}
        <InsightsSection isCurrentUser={isCurrentUser} user={user} />
        <UserStatistics user={user} />
        <RatingChangeStats statsData={statsData} />
        <UserAchievement user={user} />
        <ProfileRecentGames
          recentGames={recentGames}
          isCurrentUser={isCurrentUser}
          user={user}
        />
        <SocialLinks links={user?.links} />
      </ScrollView>

      <LogoutOverlay
        isVisible={isOverlayVisible}
        toggleOverlay={toggleOverlay}
        confirmLogout={confirmLogout}
        title={
          "Are you sure you want to delete your account? Once deleted, you can't retrieve your history."
        }
      />
    </View>
  );
};

CompactProfilePage.propTypes = {
  signOut: PropTypes.func,
  user: PropTypes.object,
  recentGames: PropTypes.array,
  statsData: PropTypes.array,
  isCurrentUser: PropTypes.bool,
  userAdditionalInfo: PropTypes.object,
};

export default React.memo(CompactProfilePage);
