import React, { useState, useCallback, useEffect } from 'react'
import { useSession } from '@/src/modules/auth/containers/AuthProvider'
import useMediaQuery from 'core/hooks/useMediaQuery'
import PropTypes from 'prop-types'
import _isEmpty from 'lodash/isEmpty'
import _isNil from 'lodash/isNil'
import ErrorView from 'atoms/ErrorView'
import useUserGames from '../../hooks/query/useUserGames'
import CompactProfilePage from './Compact'
import ExpandedProfilePage from './Expanded'
import ProfilePageShimmer from '../../shimmers/ProfilePageShimmer/ProfilePageShimmer'
import useGetUserByUserNameQuery from '../../../../core/hooks/useGetUserByUsername'

const ProfilePage = (props) => {
  const { user, isCurrentUser, userAdditionalInfo } = props

  const { signOut } = useSession()

  const { isMobile: isCompactMode } = useMediaQuery()

  const { loading, error, fetchPageGames } = useUserGames({
    user,
    pageSize: 10,
  })

  const [statsData, setStatsData] = useState([])

  const [recentGames, setRecentGames] = useState([])

  const loadStatsAndRecentGamesData = useCallback(async () => {
    const { statsData, recentGames } = await fetchPageGames({ pageNumber: 0 })
    setStatsData(statsData)
    setRecentGames(recentGames)
  }, [])

  useEffect(() => {
    loadStatsAndRecentGamesData()
  }, [])

  const ProfilePageComponent = isCompactMode
    ? CompactProfilePage
    : ExpandedProfilePage

  if (loading) {
    return <ProfilePageShimmer />
  }

  return (
    <ProfilePageComponent
      key="User-Profile-Page"
      signOut={signOut}
      statsData={statsData}
      recentGames={recentGames}
      isCurrentUser={isCurrentUser}
      userAdditionalInfo={userAdditionalInfo}
      user={user}
    />
  )
}

ProfilePage.propTypes = {
  user: PropTypes.object,
  isCurrentUser: PropTypes.bool,
  userAdditionalInfo: PropTypes.object,
}

const ProfilePageContainer = (props) => {
  const { username } = props

  const { user, loading, error, isCurrentUser, userAdditionalInfo } =
    useGetUserByUserNameQuery({ userName: username })

  if (loading) {
    return <ProfilePageShimmer />
  }

  if (_isEmpty(user) || _isNil(user) || error) {
    return (
      <ErrorView errorMessage={`User not Found with username ${username}`} />
    )
  }

  return (
    <ProfilePage
      user={user}
      isCurrentUser={isCurrentUser}
      userAdditionalInfo={userAdditionalInfo}
    />
  )
}

ProfilePageContainer.propTypes = {
  username: PropTypes.string,
}

export default React.memo(ProfilePageContainer)
