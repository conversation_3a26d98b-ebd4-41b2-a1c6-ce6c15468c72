import React, { useCallback, useEffect, useState } from 'react'
import { Text, View } from 'react-native'
import dark from '../../../../../../core/constants/themes/dark'
import { LineChart } from 'react-native-chart-kit'
import PropTypes from 'prop-types'
import useStatsGraphStyles from './StatsGraph.style'
import _isNil from 'lodash/isNil'
import _isEmpty from 'lodash/isEmpty'
import _map from 'lodash/map'
import _round from 'lodash/round'
import _mean from 'lodash/mean'

const StatsGraph = (props) => {
    const { userStatsData, globalStatsData } = props
    const styles = useStatsGraphStyles()
    const [graphWidth, setGraphWidth] = useState(300)
    const [averageTime, setAverageTime] = useState(0)
    const [averageAccuracy, setAverageAccuracy] = useState(0)

    const onGraphLayout = useCallback((event) => {
        const { width } = event.nativeEvent.layout
        setGraphWidth(width)
    }, [])

    const chartConfig = {
        backgroundColor: dark.colors.gradientBackground,
        backgroundGradientFrom: dark.colors.gradientBackground,
        backgroundGradientTo: dark.colors.gradientBackground,
        decimalPlaces: 2,
        color: () => 'white',
        labelColor: () => 'white',
        strokeWidth: 2,
        style: { borderRadius: 16 },
        propsForDots: {
            r: '2',
            stroke: dark.colors.secondary,
            strokeWidth: '1',
        },
        propsForBackgroundLines: { strokeDasharray: '' },
        fillShadowGradientFrom: dark.colors.primary,
        fillShadowGradientTo: dark.colors.primary,
    }

    const prepareGraphData = useCallback(() => {
        const userStats = _map(userStatsData, 'userPresetStats')
        const globalStats = [globalStatsData]

        // console.info('Deepak userStats : ', userStats)
        // console.info('Deepak globalStats : ', globalStats)

        const maxLength = Math.max(userStats.length, globalStats.length)

        const paddedUserStats =
            userStats.length < maxLength
                ? [
                      ...userStats,
                      ...Array(maxLength - userStats.length).fill(
                          userStats[userStats.length - 1]
                      ),
                  ]
                : userStats

        const paddedGlobalStats =
            globalStats.length < maxLength
                ? [
                      ...globalStats,
                      ...Array(maxLength - globalStats.length).fill(
                          globalStats[globalStats.length - 1]
                      ),
                  ]
                : globalStats

        const userTimeData = _map(paddedUserStats, (stat) => {
            const time = _round(Math.floor(stat?.avgTime) / 1000, 2)
            return !isNaN(time) ? time : 0
        })

        const globalTimeData = _map(paddedGlobalStats, (stat) => {
            const time = _round(Math.floor(stat?.globalAverageTime) / 1000, 2)
            return !isNaN(time) ? time : 0
        })

        const userAccuracyData = _map(paddedUserStats, (stat) => {
            const accuracy = stat?.avgAccuracy
            return !isNaN(accuracy) ? accuracy : 0
        })

        const globalAccuracyData = _map(paddedGlobalStats, (stat) => {
            const accuracy = stat?.globalAccuracy
            return !isNaN(accuracy) ? accuracy : 0
        })

        return {
            userTimeData,
            globalTimeData,
            userAccuracyData,
            globalAccuracyData,
        }
    }, [userStatsData, globalStatsData])

    useEffect(() => {
        const {
            userTimeData,
            globalTimeData,
            userAccuracyData,
            globalAccuracyData,
        } = prepareGraphData()

        setAverageTime(_round(_mean(userTimeData), 2))
        setAverageAccuracy(_round(_mean(userAccuracyData), 2))
    }, [prepareGraphData])

    const timeGraphData = {
        labels: [],
        datasets: [
            {
                data: prepareGraphData().userTimeData,
                color: () => dark.colors.secondary,
                strokeWidth: 2,
            },
            {
                data: prepareGraphData().globalTimeData,
                color: () => dark.colors.graphRedBg,
                strokeWidth: 2,
            },
        ],
    }

    const accuracyGraphData = {
        labels: [],
        datasets: [
            {
                data: prepareGraphData().userAccuracyData,
                color: () => dark.colors.secondary,
                strokeWidth: 2,
            },
            {
                data: prepareGraphData().globalAccuracyData,
                color: () => dark.colors.graphRedBg,
                strokeWidth: 2,
            },
        ],
    }

    return (
        <View>
            {timeGraphData.datasets[0].data.length > 0 && (
                <View style={styles.section} onLayout={onGraphLayout}>
                    <Text style={styles.sectionTitle}>AVERAGE TIME</Text>
                    <Text
                        style={[
                            styles.highlightedText,
                            {
                                color: 'white',
                                textAlign: 'left',
                                fontSize: 15,
                                lineHeight: 18,
                                fontFamily: 'Montserrat-700',
                            },
                        ]}
                    >
                        {averageTime} Sec
                    </Text>
                    <Text style={styles.highlightedText}>
                        Global ({' '}
                        {
                            <View
                                style={{
                                    backgroundColor: dark.colors.graphRedBg,
                                    height: 3,
                                    width: 20,
                                }}
                            />
                        }{' '}
                        ) Yours ({' '}
                        {
                            <View
                                style={{
                                    backgroundColor: dark.colors.secondary,
                                    height: 3,
                                    width: 20,
                                }}
                            />
                        }{' '}
                        )
                    </Text>
                    <LineChart
                        data={timeGraphData}
                        width={graphWidth}
                        height={220}
                        withHorizontalLines={false}
                        withVerticalLines={false}
                        withDots={false}
                        chartConfig={chartConfig}
                        style={styles.chart}
                    />
                </View>
            )}

            {accuracyGraphData.datasets[0].data.length > 0 && (
                <View style={styles.section} onLayout={onGraphLayout}>
                    <Text style={styles.sectionTitle}>ACCURACY</Text>
                    <Text
                        style={[
                            styles.highlightedText,
                            {
                                color: 'white',
                                textAlign: 'left',
                                fontSize: 15,
                                lineHeight: 18,
                                fontFamily: 'Montserrat-700',
                            },
                        ]}
                    >
                        {averageAccuracy} %
                    </Text>
                    <Text style={styles.highlightedText}>
                        Global ({' '}
                        {
                            <View
                                style={{
                                    backgroundColor: dark.colors.graphRedBg,
                                    height: 3,
                                    width: 20,
                                }}
                            />
                        }{' '}
                        ) Yours ({' '}
                        {
                            <View
                                style={{
                                    backgroundColor: dark.colors.secondary,
                                    height: 3,
                                    width: 20,
                                }}
                            />
                        }{' '}
                        )
                    </Text>
                    <LineChart
                        data={accuracyGraphData}
                        width={graphWidth}
                        height={220}
                        withHorizontalLines={false}
                        withVerticalLines={false}
                        withDots={false}
                        chartConfig={chartConfig}
                        style={styles.chart}
                    />
                </View>
            )}
        </View>
    )
}

StatsGraph.propTypes = {
    userStatsData: PropTypes.object.isRequired,
    globalStatsData: PropTypes.object.isRequired,
}

const StatsGraphContainer = (props) => {
    const { userStatsData, globalStatsData } = props
    if (
        _isNil(userStatsData) ||
        _isEmpty(userStatsData) ||
        _isNil(globalStatsData) ||
        _isEmpty(globalStatsData)
    ) {
        return null
    }
    return <StatsGraph {...props} />
}

export default StatsGraphContainer
