import { gql, useLazyQuery } from '@apollo/client';
import { useCallback } from 'react';
import { USER_PUBLIC_DETAIL_FRAGMENT } from 'core/graphql/fragments/userPublicDetail';

const GET_USER_GAMES_BY_RATING_TYPE_QUERY = gql`
  ${USER_PUBLIC_DETAIL_FRAGMENT}
  query GetUserGamesByRatingType($payload: GetGamesByRatingInput!) {
    getUserGamesByRatingType(payload: $payload) {
      games {
        _id
        startTime
        players {
          userId
          rating
          status
        }
        config {
          timeLimit
          numPlayers
          gameType
        }
        leaderBoard {
          userId
          correct
          incorrect
          ratingChange
          rank
        }
      }
      puzzleGames {
        _id
        startTime
        players {
          userId
          rating
          status
        }
        leaderBoard {
          userId
          correct
          incorrect
          ratingChange
          rank
        }
        config {
          timeLimit
          numPlayers
          gameType
        }
      }
      users {
        ...UserPublicDetailFields
      }
      totalCount
    }
  }
`;

const DEFAULT_PAGE_SIZE = 10;

const useGetUserGamesByRatingType = ({
  userId,
  ratingType,
  pageSize = DEFAULT_PAGE_SIZE,
}) => {
  const [fetchRawGamesQuery, { loading, error, data, refetch }] = useLazyQuery(
    GET_USER_GAMES_BY_RATING_TYPE_QUERY,
    {
      notifyOnNetworkStatusChange: true,
      fetchPolicy: 'cache-and-network',
    },
  );

  const executeFetch = useCallback(
    ({ pageNumber, pageSize }) => {
      if (!userId || !ratingType) return Promise.resolve(null);

      return fetchRawGamesQuery({
        variables: {
          payload: {
            userId,
            pageInfo: {
              pageNumber,
              rows: pageSize,
            },
            ratingType,
          },
        },
      });
    },
    [userId, ratingType, fetchRawGamesQuery],
  );

  return {
    loading,
    error,
    rawData: data?.getUserGamesByRatingType,
    fetchGames: executeFetch,
    refetch,
  };
};

export default useGetUserGamesByRatingType;
