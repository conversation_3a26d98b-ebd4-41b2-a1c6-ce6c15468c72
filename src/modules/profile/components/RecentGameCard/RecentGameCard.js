import React, { useCallback, useMemo } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { Text } from '@rneui/themed';
import UserImage from '@/src/components/atoms/UserImage';
import Dark from '@/src/core/constants/themes/dark';
import PropTypes from 'prop-types';
import { useRouter } from 'expo-router';
import _toNumber from 'lodash/toNumber';
import { formatPastTime } from 'core/utils/dateUtils';
import _isEqual from 'lodash/isEqual';
import userReader from '../../../../core/readers/userReader';
import styles from './RecentGameCard.style';

const RecentGameCard = (props) => {
  const { recentGame, isCurrentUser } = props;
  const router = useRouter();

  const { currentPlayer, opponentPlayer, startTime } = recentGame;
  const win = currentPlayer?.rank === 1;

  const navigateToGame = useCallback(
    ({ gameId }) => {
      if (!isCurrentUser) {
        return;
      }
      router.push(`/game/${gameId}/result`);
    },
    [router, isCurrentUser],
  );

  const isTied = _isEqual(currentPlayer?.correct, opponentPlayer?.correct);

  const borderColor = useMemo(() => {
    if (isTied) {
      return Dark.colors.textDark;
    }
    if (win) {
      return Dark.colors.secondary;
    }

    return Dark.colors.red;
  }, [win, isTied]);

  const textOfWinOrLoss = useMemo(() => {
    if (isTied) {
      return 'T';
    }
    if (win) {
      return 'W';
    }
    return 'L';
  }, [win, isTied]);

  return (
    <TouchableOpacity
      style={styles.background}
      onPress={() => navigateToGame({ gameId: recentGame?._id })}
    >
      <View style={styles.card}>
        <View style={styles.imageContainer}>
          <UserImage
            user={opponentPlayer}
            size={32}
            rounded={false}
            style={styles.userImage}
          />
        </View>
        <View style={styles.userInfo}>
          <Text style={styles.userName} numberOfLines={1}>
            {userReader.username(opponentPlayer)} ({opponentPlayer?.rating})
          </Text>
          <View style={styles.profileDescription}>
            <Text style={styles.thenRating}>
              {_toNumber(currentPlayer?.rating) +
                _toNumber(currentPlayer?.ratingChange)}{' '}
            </Text>
            {startTime ? (
              <Text style={styles.gameTime}>({formatPastTime(startTime)})</Text>
            ) : null}
          </View>
        </View>
      </View>
      <View style={styles.endContainer}>
        <View style={[styles.WinOrLoss, { borderColor }]}>
          <Text style={styles.WLText}>{textOfWinOrLoss}</Text>
        </View>
        <View style={[styles.WinOrLoss, { borderColor, width: 40 }]}>
          <Text style={styles.WLText}>
            {win ? '+' : '-'}
            {Math.abs(currentPlayer?.ratingChange)}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

RecentGameCard.propTypes = {
  recentGame: PropTypes.shape({
    currentPlayer: PropTypes.object,
    opponentPlayer: PropTypes.object,
  }),
  isCurrentUser: PropTypes.bool,
};

export default React.memo(RecentGameCard);
