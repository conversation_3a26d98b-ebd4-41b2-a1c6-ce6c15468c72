import dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    // flex: 1,
    maxHeight: 240,
    borderColor: dark.colors.tertiary,
    borderWidth: 1,
    paddingBottom: 20,
    borderRadius: 10,
  },
  buttonText: {
    fontFamily: 'Montserrat-500',
    fontSize: 14,
    color: 'white',
    textAlign: 'left',
  },
  buttonStyle: {
    borderColor: dark.colors.tertiary,
    borderWidth: 2,
    height: 40,
    marginHorizontal: 16,
    borderRadius: 12,
    backgroundColor: dark.colors.gradientBackground,
  },
  creationFieldsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    width: '100%',
    backgroundColor: dark.colors.background,
    borderWidth: 1,
    gap: 16,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    borderColor: dark.colors.tertiary,
  },
  labelStyle: {
    color: dark.colors.secondary,
    fontFamily: 'Montserrat-500',
    fontSize: 13,
    textAlign: 'center',
  },
  detailedInfoContainer: {
    flexDirection: 'row',
    // flex: 1,

    gap: 8,
  },
  detailValueText: {
    fontFamily: 'Montserrat-600',
    color: 'white',
    fontSize: 12,
    lineHeight: 15,
    textAlign: 'left',
  },
  detailTextContainer: {
    gap: 2,
  },
  detailLabelText: {
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
    fontSize: 10,
    lineHeight: 13,
    textAlign: 'left',
  },
  detailIconContainer: {
    height: 40,
    width: 40,
    borderRadius: 6,
    backgroundColor: dark.colors.primary,
  },
});

export default styles;
