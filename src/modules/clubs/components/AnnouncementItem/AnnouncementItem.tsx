import React from 'react';
import { Text, View } from 'react-native';
import UserImage from '@/src/components/atoms/UserImage';
import { Announcement } from '../../hooks/queries/useGetClubAnnouncements';
import styles from './AnnouncementItem.style';
import calculateDuration from '../../utils/calculateDuration';

const AnnouncementItem = ({ announcement }: { announcement: Announcement }) => (
  <View style={styles.mainContainer}>
    <View style={styles.container}>
      <UserImage
        user={announcement.creatorInfo}
        style={styles.userImage}
        rounded={false}
      />

      <View style={styles.contentContainer}>
        <Text style={styles.titleText} numberOfLines={1}>
          {announcement.title}
        </Text>
        <Text style={styles.descText}>{announcement.content}</Text>
      </View>
    </View>
    <View style={{ alignItems: 'flex-end', justifyContent: 'center' }}>
      <Text style={styles.userNameText}>
        {announcement.creatorInfo.username}{' '}
        <Text style={styles.passedDaysText}>
          ( {calculateDuration({ createdAt: announcement.createdAt })} ago )
        </Text>
      </Text>
    </View>
  </View>
);

export default React.memo(AnnouncementItem);
