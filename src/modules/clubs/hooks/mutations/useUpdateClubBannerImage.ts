import { base64ToFile } from '@/src/core/utils/base64DataToFile';
import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';

const UPDATE_CLUB_BANNER_IMAGE_MUTATION = gql`
  mutation UploadClubLogoImage($clubId: ID!, $file: Upload!) {
    uploadClubBannerImage(clubId: $clubId, file: $file) {
      url
    }
  }
`;

const useUpdateClubBannerImage = () => {
  const [updateClubBannerImageMutationQuery, { loading, error }] = useMutation(
    UPDATE_CLUB_BANNER_IMAGE_MUTATION,
  );

  const updateClubBannerImage = useCallback(
    async ({
      clubBannerImageUri,
      clubId,
    }: {
      clubBannerImageUri: any;
      clubId: string;
    }) => {
      const imageFile = base64ToFile({
        fileUri: clubBannerImageUri,
        fileName: 'banner_image.jpeg',
      });
      try {
        await updateClubBannerImageMutationQuery({
          variables: {
            clubId,
            file: imageFile,
          },
        });
        return true;
      } catch (e) {
        return false;
      }
    },
    [updateClubBannerImageMutationQuery],
  );
  return {
    updateClubBannerImage,
    isUpdatingClubBannerImage: loading,
    error,
  };
};

export default useUpdateClubBannerImage;
