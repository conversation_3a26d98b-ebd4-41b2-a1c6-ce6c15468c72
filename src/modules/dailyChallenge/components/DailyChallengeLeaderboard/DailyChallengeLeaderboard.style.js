import { StyleSheet } from 'react-native'
import Dark from 'core/constants/themes/dark'
import APP_LAYOUT_CONSTANTS from 'core/constants/appLayout'
import dark from '../../../../core/constants/themes/dark'

const styles = StyleSheet.create({
    container: {
        flex: 1,
        width: '100%',
        maxWidth: APP_LAYOUT_CONSTANTS.CENTER_PANE_MAX_WIDTH,
        height: '100%',
        paddingHorizontal: 16,
        justifyContent: 'flex-start',
    },
    flatListContainerStyle: {
        width: '100%',
    },
    flatListContentContainerStyle: {
        paddingHorizontal: 24,
        width: '100%',
    },
    userListStyle: {
        paddingHorizontal: 24,
    },
    heading: {
        fontSize: 24,
        fontFamily: 'Montserrat-700',
        marginBottom: 16,
    },
    item: {
        padding: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#ccc',
    },
    loadingIndicator: {
        marginTop: 50,
    },
    textContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    text: {
        color: 'white',
    },


    // header
    headerContainer: {
        // marginTop: -8,
        // paddingVertical: 12,
        marginBottom: 10,
        borderBottomColor: dark.colors.tertiary,
        borderBottomWidth: 1
    },
    headerLabelStyle: {
        color: Dark.colors.textDark,
        fontSize: 12,
    },
    // columns
    rowContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 12,
        gap: 6,
    },
    mobileRowContainer: {
        paddingVertical: 10,
    },
    compactRowContainer: {
        paddingVertical: 5,
    },
    rankColumn: {
        width: 36,
    },
    rowLabel: {
        color: 'white',
        maxWidth: 180,
        fontSize: 14,
        fontFamily: 'Montserrat-600',
    },
    rating: {
        color: Dark.colors.textDark,
        fontSize: 14,
    },
    profileInfoColumn: {
        flex: 1,
        flexBasis: 1,
        flexDirection: 'row',
        gap: 6,
        alignItems: 'center',
    },
    timeColumn: {
        flexShrink: 1,
        width: 70,
        alignItems: 'flex-end',
    },

    separator: {
        width: '100%',
        height: 1,
        backgroundColor: Dark.colors.tertiary,
        marginVertical: 4,
    },

    // empty leaderboard
    emptyLeaderboardContainer: {
        alignItems: 'center',
        padding: 24,
        gap: 16,
    },
    emptyLeaderboardLabel: {
        color: Dark.colors.textDark,
        fontSize: 10,
        letterSpacing: 1,
        fontFamily: "Montserrat-800",
        lineHeight: 20,
        textAlign: 'center',
    },
    emptyLeaderboardText: {
        color: Dark.colors.textDark,
        fontSize: 13,
        letterSpacing: 1,
        fontFamily: "Montserrat-400",
        lineHeight: 20,
        textAlign: 'center',
    },
    playNowButton: {
        width: 100,
        borderRadius: 16,
        backgroundColor: Dark.colors.tertiary
    },
    playNowLabel: {
        fontSize: 12,
        color: 'white'
    },
    usernameContainer: {
        flex: 1,
        flexDirection: 'row',
        justifyContent: 'flex-start',
        flexWrap: 'wrap',
        gap: 6,
    },
    usernameContainerCompact: {
        justifyContent: 'center',
        flexDirection: 'column',
        gap: 2,
    },
    rowLabelCompact: {
        fontSize: 14,
        fontFamily: 'Montserrat-400',
        maxWidth: 120
    },
    ratingCompact: {
        fontSize: 11,
        fontFamily: 'Montserrat-600',
    },

    emptyLeaderboardInfoText: {
        fontSize: 10,
        fontFamily: "Montserrat-500",
        lineHeight: 16,
        textAlign: 'center',
        maxWidth: 245,
        color: dark.colors.textDark
    },
    takeChallengeText: {
        fontSize: 12,
        fontFamily: "Montserrat-600",
        lineHeight: 20,
        textAlign: 'center',
        color: dark.colors.secondary
    }
})

export default styles
