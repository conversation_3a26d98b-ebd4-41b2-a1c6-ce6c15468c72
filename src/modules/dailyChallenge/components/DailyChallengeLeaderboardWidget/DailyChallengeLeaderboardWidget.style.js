import { StyleSheet } from "react-native";
import { withOpacity } from 'core/utils/colorUtils'
import Dark from "core/constants/themes/dark";

const CARD_BORDER_RADIUS = 10;

export default StyleSheet.create({
    container: {
        backgroundColor: Dark.colors.background,
    },
    contentContainer: {
        backgroundColor: withOpacity(Dark.colors.gradientBackground, 0.5),
        padding: 16,
        paddingBottom: 20,
        borderRadius: CARD_BORDER_RADIUS,
        gap: 8,
        borderColor:Dark.colors.tertiary,
        borderWidth:2
    },
    label: {
        fontSize: 12,
        fontFamily: 'Montserrat-600',
        color: Dark.colors.textDark
    },
    contentContainerStyle: { paddingHorizontal: 0 },
    rankColumnStyle: {
        width: 16,
    },
    liveContainer: {
        flexDirection: 'row',
        gap: 8,
    },
    liveText: {
        color: Dark.colors.red,
        fontSize: 10,
    },
    headerContainer: {
        marginTop: 0,
        paddingVertical: 0,
    },
    seeMoreButtonLabel: {
        color: Dark.colors.secondary,
        fontSize: 12,
    },
    headerRow:{
        justifyContent:'space-between',
        flexDirection:"row",
        alignItems:"center"
    }
})